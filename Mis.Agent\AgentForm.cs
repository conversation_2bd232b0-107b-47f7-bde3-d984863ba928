using Mis.Shared.Interface;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System;
using System.Reflection;
using System.Windows.Forms;
using System.Xml.Linq;

namespace Mis.Agent
{
    public partial class AgentForm : Form
    {
        private IServiceProvider _serviceProvider;
        private IHost _webHost;
        public AgentForm()
        {
            InitializeComponent();
            tabControl1.SelectedIndexChanged += TabControl1_SelectedIndexChanged; // Attach the event handler

        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            if (_webHost != null)
            {
                _webHost.StopAsync();
                _webHost.Dispose();
            }
            base.OnFormClosing(e);
        }
        private async void TabControl1_SelectedIndexChanged(object? sender, EventArgs e)
        {
            try
            {
                RefreshDataGridView();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"An error occurred while refreshing data: {ex.Message}");
            }
        }


        private async void RefreshDataGridView()
        {
            try
            {
                // Fetch updated data and bind it to the DataGridView
                //var notifications = _publicNotificationAppService.GetAllNotificationsAsync().Result; // Implement this method
                var notifications = await SerialPortManager.Instance.GetAllNotificationsAsync();

                var notificationsTab = tabControl1.TabPages["NotificationsTab"] as TabPage;
                if (notificationsTab != null)
                {
                    var dataGridView = notificationsTab.Controls["dataGridView1"] as DataGridView;
                    if (dataGridView != null)
                    {
                        if (dataGridView.InvokeRequired)
                        {
                            dataGridView.Invoke(new Action(() =>
                            {
                                dataGridView.DataSource = notifications;
                                dataGridView.Refresh();
                            }));
                        }
                        else
                        {
                            dataGridView.DataSource = notifications;
                            dataGridView.Refresh();
                        }
                    }
                    else
                    {
                        Console.WriteLine("PictureBox not found.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    Console.WriteLine("ScannerTab not found.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error refreshing data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }



        private async void AgentForm_Load(object sender, EventArgs e)
        {
            string pluginPath = Path.Combine(Application.StartupPath, "Plugins");
            var services = new ServiceCollection();
            if (!Directory.Exists(pluginPath))
            {
                MessageBox.Show("Plugins folder not found.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }


            // Create plugin manager
            var serviceProvider = services.BuildServiceProvider();
            var pluginManager = new PluginManager(serviceProvider);

            // Load all plugins
            pluginManager.LoadPlugins(pluginPath);

            // Add TabPages for all plugins
            foreach (var plugin in pluginManager.GetAllPlugins())
            {
                try
                {
                    object tabPageObj = plugin.GetTabPage();
                    if (tabPageObj is TabPage tabPage)
                    {
                        tabPage.Text = plugin.PluginName; // Set tab title to plugin name
                        tabControl1.TabPages.Add(tabPage);
                        Console.WriteLine($"Added tab for plugin: {plugin.PluginName} v{plugin.PluginVersion}");
                    }
                    else
                    {
                        MessageBox.Show($"Plugin {plugin.PluginName} did not return a valid TabPage.", "Plugin Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error loading plugin {plugin.PluginName}: {ex.Message}");
                }
            }
        }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error loading plugin from {file}: {ex.Message}", "Plugin Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
}
        }
    }
}
