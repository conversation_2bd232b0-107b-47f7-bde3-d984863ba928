using Mis.Shared.Interface;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System;
using System.Reflection;
using System.Windows.Forms;
using System.Xml.Linq;

namespace Mis.Agent
{
    public partial class AgentForm : Form
    {
        private IServiceProvider _serviceProvider;
        private IHost _webHost;
        public AgentForm()
        {
            InitializeComponent();
            tabControl1.SelectedIndexChanged += TabControl1_SelectedIndexChanged; // Attach the event handler

        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            if (_webHost != null)
            {
                _webHost.StopAsync();
                _webHost.Dispose();
            }
            base.OnFormClosing(e);
        }
        private async void TabControl1_SelectedIndexChanged(object? sender, EventArgs e)
        {
            try
            {
                RefreshDataGridView();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"An error occurred while refreshing data: {ex.Message}");
            }
        }


        private async void RefreshDataGridView()
        {
            try
            {
                // Fetch updated data and bind it to the DataGridView
                //var notifications = _publicNotificationAppService.GetAllNotificationsAsync().Result; // Implement this method
                var notifications = await SerialPortManager.Instance.GetAllNotificationsAsync();

                var notificationsTab = tabControl1.TabPages["NotificationsTab"] as TabPage;
                if (notificationsTab != null)
                {
                    var dataGridView = notificationsTab.Controls["dataGridView1"] as DataGridView;
                    if (dataGridView != null)
                    {
                        if (dataGridView.InvokeRequired)
                        {
                            dataGridView.Invoke(new Action(() =>
                            {
                                dataGridView.DataSource = notifications;
                                dataGridView.Refresh();
                            }));
                        }
                        else
                        {
                            dataGridView.DataSource = notifications;
                            dataGridView.Refresh();
                        }
                    }
                    else
                    {
                        Console.WriteLine("PictureBox not found.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    Console.WriteLine("ScannerTab not found.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error refreshing data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }



        private async void AgentForm_Load(object sender, EventArgs e)
        {
            string pluginPath = Path.Combine(Application.StartupPath, "Plugins");
            var services = new ServiceCollection();
            if (!Directory.Exists(pluginPath))
            {
                MessageBox.Show("Plugins folder not found.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }


            foreach (var file in Directory.GetFiles(pluginPath, "*.dll"))
            {
                try
                {
                    var assembly = Assembly.LoadFrom(file);
                    Type[] types;
                    try
                    {
                        types = assembly.GetTypes(); // Attempt to load types
                    }
                    catch (ReflectionTypeLoadException ex)
                    {
                        types = ex.Types.Where(t => t != null).ToArray(); // Load only valid types
                        foreach (var loaderException in ex.LoaderExceptions)
                        {
                            Console.WriteLine($"Error loading type: {loaderException.Message}");
                        }
                    }

                    foreach (var type in types)
                    {
                        try
                        {
                            if (typeof(IPlugin).IsAssignableFrom(type) && !type.IsAbstract)
                            {
                                try
                                {
                                    // Resolve the plugin via DI
                                    var pluginInstance = ActivatorUtilities.CreateInstance(services.BuildServiceProvider(), type) as IPlugin;

                                    if (pluginInstance == null)
                                    {
                                        Console.WriteLine($"Failed to instantiate plugin of type {type.FullName}.");
                                        continue;
                                    }

                                    // Add TabPage using reflection
                                    object tabPageObj = pluginInstance.GetTabPage();
                                    if (tabPageObj is TabPage tabPage) // Safely cast to TabPage
                                    {
                                        tabControl1.TabPages.Add(tabPage);
                                    }
                                    else
                                    {
                                        MessageBox.Show($"Plugin did not return a valid TabPage.", "Plugin Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"Error instantiating plugin: {ex.Message}");
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error processing type {type.FullName}: {ex.Message}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error loading plugin from {file}: {ex.Message}", "Plugin Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
        }
    }
}
