﻿using Mis.Shared.Interface;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Data.SQLite;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data;

namespace Mis.Agent.Print
{
    public class NotificationsService : INotificationAppService
    {
        private string _databaseFile;
        private bool _notificationsEnabled = false;
        private readonly object _lock = new object();

        //public event Action NotificationUpdated;

        public NotificationsService()
        {
            var baseDirectory = AppContext.BaseDirectory;
            _databaseFile = Path.Combine(baseDirectory, "notifications.db");

            EnsureDatabaseAndTables();
            NotificationManager.NotificationEvent += OnNotificationReceived;
        }

        private void OnNotificationReceived(object sender, NotificationEventArgs e)
        {
            _notificationsEnabled = e.IsEnabled; // Additional logic based on notification state
        }

        public void ShowNotification(string title, string text)
        {
            if (_notificationsEnabled)
            {
                using (var notifyIcon = new NotifyIcon
                {
                    Icon = SystemIcons.Information,
                    Visible = true,
                    BalloonTipTitle = title,
                    BalloonTipText = text
                })
                {
                    notifyIcon.ShowBalloonTip(1000);
                    Task.Delay(1000).ContinueWith(t => notifyIcon.Dispose());
                }
            }
        }
        public string PluginName => "Notifications Plugin";
        public string PluginVersion => "1.0.0";

        public async Task Execute()
        {
            await Task.CompletedTask;
        }
        public object GetTabPage()
        {
            var notificationsForm = new NotificationsForm(this);
            notificationsForm.PopulateForm();
            return notificationsForm.NotificationsTab;
        }

        private void EnsureDatabaseAndTables()
        {

            if (!File.Exists(_databaseFile))
            {
                // Create the database file
                SQLiteConnection.CreateFile(_databaseFile);
            }

            // Create tables if they don't exist
            using (var connection = new SQLiteConnection($"Data Source={_databaseFile};Version=3;"))
            {
                connection.Open();

                var createNotificationsTableQuery = @"
                CREATE TABLE IF NOT EXISTS Notifications (
                    Id TEXT PRIMARY KEY,
                    No TEXT NOT NULL,
                    HtmlContent TEXT,
                    IsPrinted INTEGER NOT NULL,
                    ReceiveTime DATETIME NOT NULL
                )";
                var createLogsTableQuery = @"
                CREATE TABLE IF NOT EXISTS Logs (
                  Id INTEGER PRIMARY KEY AUTOINCREMENT,
                  Message TEXT NOT NULL,
                  CreatedDate DATETIME NOT NULL,
                  Level TEXT DEFAULT 'INFO'
                )";

                using (var command = new SQLiteCommand(createNotificationsTableQuery, connection))
                {
                    command.ExecuteNonQuery();
                }

                using (var command = new SQLiteCommand(createLogsTableQuery, connection))
                {
                    command.ExecuteNonQuery();
                }
            }
        }

        /// <summary>
        /// Add log entry to database
        /// </summary>
        public async Task LogMessageAsync(string message, string level = "INFO")
        {
            try
            {
                using (var connection = new SQLiteConnection($"Data Source={_databaseFile};Version=3;"))
                {
                    await connection.OpenAsync();

                    var insertQuery = @"
                        INSERT INTO Logs (Message, CreatedDate, Level)
                        VALUES (@Message, @CreatedDate, @Level)";

                    using (var command = new SQLiteCommand(insertQuery, connection))
                    {
                        command.Parameters.AddWithValue("@Message", message);
                        command.Parameters.AddWithValue("@CreatedDate", DateTime.Now);
                        command.Parameters.AddWithValue("@Level", level);

                        await command.ExecuteNonQueryAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                // Don't throw exceptions from logging to avoid infinite loops
                Console.WriteLine($"Failed to log message: {ex.Message}");
            }
        }

        /// <summary>
        /// Get recent log entries
        /// </summary>
        public async Task<List<LogEntry>> GetRecentLogsAsync(int count = 100)
        {
            var logs = new List<LogEntry>();

            try
            {
                using (var connection = new SQLiteConnection($"Data Source={_databaseFile};Version=3;"))
                {
                    await connection.OpenAsync();

                    var query = @"
                        SELECT Id, Message, CreatedDate, Level
                        FROM Logs
                        ORDER BY CreatedDate DESC
                        LIMIT @Count";

                    using (var command = new SQLiteCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@Count", count);

                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                logs.Add(new LogEntry
                                {
                                    Id = reader.GetInt32("Id"),
                                    Message = reader.GetString("Message"),
                                    CreatedDate = reader.GetDateTime("CreatedDate"),
                                    Level = reader.IsDBNull("Level") ? "INFO" : reader.GetString("Level")
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to retrieve logs: {ex.Message}");
            }

            return logs;
        }

        /// <summary>
        /// Clear all log entries from database
        /// </summary>
        public async Task ClearAllLogsAsync()
        {
            try
            {
                using (var connection = new SQLiteConnection($"Data Source={_databaseFile};Version=3;"))
                {
                    await connection.OpenAsync();

                    var deleteQuery = "DELETE FROM Logs";
                    using (var command = new SQLiteCommand(deleteQuery, connection))
                    {
                        int deletedRows = await command.ExecuteNonQueryAsync();
                        Console.WriteLine($"Cleared {deletedRows} log entries");
                    }

                    // Reset auto-increment counter
                    var resetQuery = "DELETE FROM sqlite_sequence WHERE name='Logs'";
                    using (var command = new SQLiteCommand(resetQuery, connection))
                    {
                        await command.ExecuteNonQueryAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to clear logs: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Get log statistics
        /// </summary>
        public async Task<LogStatistics> GetLogStatisticsAsync()
        {
            var stats = new LogStatistics();

            try
            {
                using (var connection = new SQLiteConnection($"Data Source={_databaseFile};Version=3;"))
                {
                    await connection.OpenAsync();

                    // Get total count
                    var countQuery = "SELECT COUNT(*) FROM Logs";
                    using (var command = new SQLiteCommand(countQuery, connection))
                    {
                        stats.TotalCount = Convert.ToInt32(await command.ExecuteScalarAsync());
                    }

                    // Get counts by level
                    var levelCountQuery = @"
                        SELECT Level, COUNT(*) as Count
                        FROM Logs
                        GROUP BY Level";

                    using (var command = new SQLiteCommand(levelCountQuery, connection))
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            var level = reader.GetString("Level");
                            var count = reader.GetInt32("Count");

                            switch (level)
                            {
                                case "INFO":
                                    stats.InfoCount = count;
                                    break;
                                case "ERROR":
                                    stats.ErrorCount = count;
                                    break;
                                case "WARNING":
                                    stats.WarningCount = count;
                                    break;
                            }
                        }
                    }

                    // Get oldest and newest dates
                    var dateQuery = @"
                        SELECT MIN(CreatedDate) as OldestDate, MAX(CreatedDate) as NewestDate
                        FROM Logs";

                    using (var command = new SQLiteCommand(dateQuery, connection))
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            if (!reader.IsDBNull("OldestDate"))
                                stats.OldestLogDate = reader.GetDateTime("OldestDate");
                            if (!reader.IsDBNull("NewestDate"))
                                stats.NewestLogDate = reader.GetDateTime("NewestDate");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to get log statistics: {ex.Message}");
            }

            return stats;
        }

        public bool DatabaseFileExists() => File.Exists(_databaseFile);

        public string GetDatabasePath() => _databaseFile;



        public bool TableExists(string tableName)
        {
            using (var connection = new SQLiteConnection($"Data Source={_databaseFile};Version=3;"))
            {
                connection.Open();
                var query = $"SELECT name FROM sqlite_master WHERE type='table' AND name='{tableName}';";

                using (var command = new SQLiteCommand(query, connection))
                {
                    var result = command.ExecuteScalar();
                    return result != null && result.ToString() == tableName;
                }
            }
        }







    }

    /// <summary>
    /// Log entry model
    /// </summary>
    public class LogEntry
    {
        public int Id { get; set; }
        public string Message { get; set; }
        public DateTime CreatedDate { get; set; }
        public string Level { get; set; }
    }

    /// <summary>
    /// Log statistics model
    /// </summary>
    public class LogStatistics
    {
        public int TotalCount { get; set; }
        public int InfoCount { get; set; }
        public int ErrorCount { get; set; }
        public int WarningCount { get; set; }
        public DateTime? OldestLogDate { get; set; }
        public DateTime? NewestLogDate { get; set; }
    }
}
