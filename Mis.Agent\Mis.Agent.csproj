﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <EnableWindowsTargeting>true</EnableWindowsTargeting>
    <AssemblyName>MIS.Agent</AssemblyName>
    <RootNamespace>MIS.Agent</RootNamespace>
    <Version>1.2.0</Version>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Product>MIS Agent System</Product>
    <Company>MIS Company</Company>
    <Copyright>Copyright © MIS Company 2024</Copyright>
    <Description>MIS Agent - Main Application with Plugin Architecture</Description>
  </PropertyGroup>


  <Target Name="CopyBuiltPluginsToPublish" AfterTargets="Publish">
    <PropertyGroup>
      <BuiltPluginDir>$(OutputPath)Plugins\</BuiltPluginDir>
      <PublishPluginDir>$(PublishDir)Plugins\</PublishPluginDir>
    </PropertyGroup>

    <!-- Create Plugins directory if it doesn't exist -->
    <MakeDir Directories="$(PublishPluginDir)" Condition="!Exists('$(PublishPluginDir)')" />

    <ItemGroup>
      <BuiltPluginFiles Include="$(BuiltPluginDir)**\*.*" />
    </ItemGroup>

    <!-- Log for debugging -->
    <Message Text="Copying Plugins from: $(BuiltPluginDir) to $(PublishPluginDir)" Importance="high" />

    <Copy SourceFiles="@(BuiltPluginFiles)"
      DestinationFiles="@(BuiltPluginFiles->'$(PublishPluginDir)%(RecursiveDir)%(Filename)%(Extension)')"
      SkipUnchangedFiles="true" />
  </Target>

  <!-- Database Cleanup Target -->
  <Target Name="CleanLogFiles" AfterTargets="Build" Condition="'$(Configuration)' == 'Debug'">
    <PropertyGroup>
      <LogsPath>$(OutputPath)logs</LogsPath>
    </PropertyGroup>

    <ItemGroup>
      <LogFilesToDelete Include="$(LogsPath)\**\*.*" Condition="Exists('$(LogsPath)')" />
    </ItemGroup>

    <Delete Files="@(LogFilesToDelete)" />

    <Message Text="All log files deleted from $(LogsPath)" Importance="high" />
  </Target>



  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Owin" Version="9.0.6" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.8.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.6" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.Async" Version="2.1.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="9.0.6" />
    <PackageReference Include="Microsoft.Windows.Compatibility" Version="9.0.6" />
    <PackageReference Include="System.Data.SQLite" Version="1.0.119" />
    <PackageReference Include="System.Security.Cryptography.Pkcs" Version="9.0.6" />
    <PackageReference Include="System.Security.Cryptography.X509Certificates" Version="4.3.2" />
    <PackageReference Include="Volo.Abp.Autofac" Version="9.0.6" />
    <PackageReference Include="PdfiumViewer.Core" Version="1.0.4" />

    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client.Core" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="9.0.6" />


  </ItemGroup>
  <ItemGroup>
    <Reference Include="Interop.WIA">
      <HintPath>WIA\Interop.WIA.dll</HintPath>
    </Reference>
    <Reference Include="IronPdf.Core">
      <HintPath>IronPdf.Core\IronPdf.Core.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <!-- Include ALL JSON files (including appsettings.json) -->
    <None Include="**\*.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </None>

    <!-- Include ALL database files -->
    <None Include="**\*.db">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </None>
  </ItemGroup>


  <ItemGroup>
    <ProjectReference
      Include="..\Plugin Agent Projects\Mis.Shared.Interface\Mis.Shared.Interface.csproj" />
  </ItemGroup>


  <Target Name="PostPublishSigning" AfterTargets="Publish"
    Condition="'$(Configuration)' == 'Release'">
    <Exec Command='powershell -ExecutionPolicy Bypass -File "$(ProjectDir)sign-and-publish.ps1"' />
  </Target>


</Project>