﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <EnableWindowsTargeting>true</EnableWindowsTargeting>
    <AssemblyName>MIS.Agent.Barcode</AssemblyName>
    <RootNamespace>MIS.Agent.Barcode</RootNamespace>
    <Version>1.0.1</Version>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Product>MIS Agent System</Product>
    <Company>MIS Company</Company>
    <Copyright>Copyright © MIS Company 2024</Copyright>
    <Description>MIS Agent Barcode Plugin - Barcode and Scanner Services</Description>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Owin" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.6" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.8.0" />
    <PackageReference Include="Volo.Abp.Core" Version="7.3.2" />
    <PackageReference Include="System.IO.Ports" Version="9.0.6" />
    <PackageReference Include="System.Management" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="9.0.6" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Mis.Shared.Interface\Mis.Shared.Interface.csproj" />
  </ItemGroup>


  <ItemGroup>
    <Reference Include="Interop.WIA">
      <HintPath>WIA\Interop.WIA.dll</HintPath>
    </Reference>
  </ItemGroup>

  <!-- Digital Signing Target -->
  <Target Name="PostBuildSigning" AfterTargets="Build" Condition="'$(Configuration)' == 'Release'">
    <PropertyGroup>
      <SignToolPath>C:\Program Files (x86)\Windows Kits\10\bin\10.0.26100.0\x64\signtool.exe</SignToolPath>
      <CertificatePath>$(ProjectDir)..\..\Mis.Agent\MisCodeSign.pfx</CertificatePath>
      <CertificatePassword>MisCompanyPassword123!</CertificatePassword>
    </PropertyGroup>

    <Exec
      Command='"$(SignToolPath)" sign /f "$(CertificatePath)" /p "$(CertificatePassword)" /tr http://timestamp.digicert.com /td sha256 /fd sha256 "$(TargetPath)"'
      Condition="Exists('$(CertificatePath)')"
      ContinueOnError="true" />
  </Target>
</Project>