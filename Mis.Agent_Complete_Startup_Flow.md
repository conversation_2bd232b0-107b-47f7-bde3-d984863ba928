# Mis.Agent Complete Startup Flow

## Mermaid Diagram Code

```mermaid
graph TD
    A[Program.Main] --> B[Load Configuration]
    B --> C[Create Host Builder]
    C --> D[Start Host in Background]
    D --> E[Create AgentStarter]
    
    E --> F[AgentStarter Constructor]
    F --> G[Initialize Event Handlers]
    G --> H[StartSignalRServer]
    G --> I[InitializeTrayIcon]
    
    H --> J[Start Web Host]
    J --> K[Execute Plugins - Background]
    
    K --> L[Create Plugin Manager]
    L --> M[Load Plugin DLLs]
    M --> N[Execute Plugin.Execute Method]
    N --> O[Plugin Background Services Start]
    
    I --> P[Create System Tray Icon]
    P --> Q[Add Context Menu]
    Q --> R[Show Tabs Form Option]
    Q --> S[Exit Option]
    
    R --> T[User Clicks Show Tabs Form]
    T --> U[ShowOrRefreshTabsFormAsync]
    U --> V{AgentForm Exists?}
    
    V -->|No| W[Create New AgentForm]
    V -->|Yes| X[Bring Existing Form to Front]
    
    W --> Y[AgentForm_Load Event]
    Y --> Z[Create Plugin Manager Again]
    Z --> AA[Load Plugins Again]
    AA --> BB[Call GetTabPage for Each Plugin]
    BB --> CC[Add TabPages to TabControl]
    CC --> DD[Show AgentForm Window]
    
    O --> EE[Plugin Services Running]
    EE --> FF[Barcode Scanner Listening]
    EE --> GG[Print Service Ready]
    EE --> HH[Notification Service Active]
    
    FF --> II[Image Captured Event]
    II --> JJ[Update Scanner Tab UI]
    
    GG --> KK[Print Request Received]
    KK --> LL[Process Print Job]
    
    HH --> MM[Notification Received]
    MM --> NN[Update Notifications Tab UI]
    
    style A fill:#e1f5fe
    style E fill:#f3e5f5
    style K fill:#fff3e0
    style Y fill:#e8f5e8
    style EE fill:#fce4ec
```

## Flow Description

### Phase 1: Application Bootstrap
1. **Program.Main** - Entry point of the application
2. **Load Configuration** - Reads appsettings.json
3. **Create Host Builder** - Sets up ASP.NET Core host for SignalR/API
4. **Start Host in Background** - Web server starts as background task
5. **Create AgentStarter** - Main application context created

### Phase 2: AgentStarter Initialization
6. **AgentStarter Constructor** - Main initialization begins
7. **Initialize Event Handlers** - Sets up event subscriptions
8. **StartSignalRServer** - Starts web host and plugin execution
9. **InitializeTrayIcon** - Creates system tray interface

### Phase 3: Background Services
10. **Start Web Host** - SignalR server becomes available
11. **Execute Plugins - Background** - Plugin background services start
12. **Create Plugin Manager** - Plugin management system initialized
13. **Load Plugin DLLs** - Discovers and loads plugin assemblies
14. **Execute Plugin.Execute Method** - Calls Execute() on each plugin
15. **Plugin Background Services Start** - Services begin running

### Phase 4: System Tray Interface
16. **Create System Tray Icon** - Tray icon appears in system tray
17. **Add Context Menu** - Right-click menu created
18. **Show Tabs Form Option** - Menu item for opening UI
19. **Exit Option** - Menu item for closing application

### Phase 5: User Interface (On Demand)
20. **User Clicks Show Tabs Form** - User requests UI
21. **ShowOrRefreshTabsFormAsync** - UI creation/display logic
22. **AgentForm Exists?** - Check if form already created
23. **Create New AgentForm** / **Bring Existing Form to Front** - Form handling
24. **AgentForm_Load Event** - Form initialization event
25. **Create Plugin Manager Again** - New plugin manager for UI
26. **Load Plugins Again** - Reload plugins for UI components
27. **Call GetTabPage for Each Plugin** - Get UI components from plugins
28. **Add TabPages to TabControl** - Add plugin UIs to main form
29. **Show AgentForm Window** - Display the main form

### Phase 6: Runtime Operations
30. **Plugin Services Running** - Background services operational
31. **Barcode Scanner Listening** - COM port monitoring active
32. **Print Service Ready** - Print job processing available
33. **Notification Service Active** - Database monitoring active
34. **Image Captured Event** - Scanner captures image
35. **Update Scanner Tab UI** - UI reflects captured image
36. **Print Request Received** - Print job arrives
37. **Process Print Job** - Print operation executed
38. **Notification Received** - New notification arrives
39. **Update Notifications Tab UI** - UI shows new notification

## Key Concepts

### Dual Plugin Loading
- **Background Loading**: Plugins loaded once for services (Execute method)
- **UI Loading**: Plugins loaded again when UI is needed (GetTabPage method)

### Separation of Concerns
- **Background Services**: Run independently of UI
- **User Interface**: Optional, on-demand component
- **System Tray**: Always available for user interaction

### Plugin Lifecycle
1. **Discovery**: Plugin DLLs found in Plugins folder
2. **Loading**: Assemblies loaded and types discovered
3. **Background Execution**: Execute() method called for services
4. **UI Creation**: GetTabPage() called when UI needed
5. **Runtime**: Services and UI operate independently

## Usage Instructions

### To view this diagram:
1. Copy the mermaid code above
2. Paste it into any Mermaid-compatible viewer:
   - [Mermaid Live Editor](https://mermaid.live/)
   - GitHub (in markdown files)
   - VS Code with Mermaid extension
   - Draw.io with Mermaid plugin

### To modify the diagram:
1. Edit the mermaid code
2. Add new nodes with unique IDs
3. Connect nodes with arrows (-->)
4. Apply styling with `style NodeID fill:#color`
