using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
namespace Mis.Shared.Interface
{
    // Base plugin interface - only essential methods
    public interface IPlugin
    {
        string PluginName { get; }
        string PluginVersion { get; }
        object GetTabPage();
        Task Execute();
        void ShowNotification(string title, string text);
    }

    // Optional interfaces for specific capabilities
    public interface IConfigurablePlugin
    {
        string GetBaseUrl();
    }

    public interface ISerialPortPlugin
    {
        string GetCOMPort();
    }

    public interface IBarcodePlugin
    {
        string GetBarcodeBaseUrl();
    }

    // Plugin metadata
    public class PluginMetadata
    {
        public string Name { get; set; }
        public string Version { get; set; }
        public string Description { get; set; }
        public Type PluginType { get; set; }
        public List<Type> SupportedInterfaces { get; set; } = new List<Type>();
    }
}