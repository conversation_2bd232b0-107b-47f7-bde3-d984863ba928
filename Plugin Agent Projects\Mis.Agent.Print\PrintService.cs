﻿using Mis.Shared.Interface;
using Mis.Agent.Print;
using System;
using System.Collections.Generic;
using System.Data.SQLite;
using System.Drawing.Printing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using PdfiumViewer;
using IronPdf;
using Microsoft.Extensions.Configuration;
using System.Runtime;
using Newtonsoft.Json;

namespace Mis.Agent.Print
{
    public class PrintService : IPrintAppService, IConfigurablePlugin, ISerialPortPlugin, IBarcodePlugin
    {
        public PrintDocument PrintDocument { get; private set; }
        PrintForm printForm;
        private readonly IConfiguration _configuration;
        PrintJobMonitor printJobMonitor;
        HtmlToPdf? renderer;
        private bool _notificationsEnabled = true;
        private TaskCompletionSource<bool> _taskCompletionSource;
        private readonly Dictionary<string, string> _settings;
        private readonly string _appsettingsFilePath;
        public event Action<bool> NotificationStateChanged;

        public PrintService()
        {
            var baseDirectory = AppContext.BaseDirectory;
            var appsettingsFilePath = Path.Combine(baseDirectory, "appsettings.json");
            if (!File.Exists(appsettingsFilePath))
            {
                MessageBox.Show($"Missing configuration: {appsettingsFilePath}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Environment.Exit(1);
            }
            _appsettingsFilePath = appsettingsFilePath;

            _configuration = new ConfigurationBuilder()
                .SetBasePath(baseDirectory)
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .Build();

            renderer = new HtmlToPdf();
            printJobMonitor = new PrintJobMonitor();
            PrintDocument = new PrintDocument();
            printForm = new PrintForm(this);
            _taskCompletionSource = new TaskCompletionSource<bool>();
            _settings = LoadSettings();
            NotificationManager.NotificationEvent += OnNotificationReceived;
        }

        private void OnNotificationReceived(object sender, NotificationEventArgs e)
        {
            _notificationsEnabled = e.IsEnabled; // Additional logic based on notification state
        }
        public string PluginName => "Print Plugin";
        public string PluginVersion => "1.0.0";

        public async Task Execute()
        {
            // Initialize print service components
            try
            {
                // Start print job monitoring
                printJobMonitor?.StartMonitoring();

                // Log plugin startup to database
                await LogAsync($"{PluginName} v{PluginVersion} initialized successfully", "INFO");

                // Log to console as well
                Console.WriteLine($"{PluginName} v{PluginVersion} initialized successfully");

                // Any other initialization logic for print service
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                await LogAsync($"Error initializing {PluginName}: {ex.Message}", "ERROR");
                Console.WriteLine($"Error initializing {PluginName}: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Log message to database
        /// </summary>
        private async Task LogAsync(string message, string level = "INFO")
        {
            try
            {
                // Get the notifications service to access logging
                var notificationService = new NotificationsService();
                await notificationService.LogMessageAsync($"[{PluginName}] {message}", level);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to log to database: {ex.Message}");
            }
        }
        public void ShowNotification(string title, string text)
        {
            if (_notificationsEnabled)
            {
                using (var notifyIcon = new NotifyIcon
                {
                    Icon = SystemIcons.Information,
                    Visible = true,
                    BalloonTipTitle = title,
                    BalloonTipText = text
                })
                {
                    notifyIcon.ShowBalloonTip(1000);
                    Task.Delay(1000).ContinueWith(t => notifyIcon.Dispose());
                }
            }
        }
        private Dictionary<string, string> LoadSettings()
        {
            try
            {
                var settings = new Dictionary<string, string>();

                // Load the PrinterEntity section
                var printerEntity = _configuration.GetSection("PrinterEntity").Get<PrinterEntity>();
                if (printerEntity != null)
                {
                    settings["DefaultPrinter"] = printerEntity.DefaultPrinter;
                    settings["DefaultPaperSize"] = printerEntity.DefaultPaperSize;
                }

                return settings;
            }
            catch (Exception ex)
            {
                throw new Exception("Error loading settings", ex);
            }
        }
        public PrinterEntity GetPrinterEntity()
        {
            return _configuration.GetSection("PrinterEntity").Get<PrinterEntity>();
        }

        public void UpdateSetting(string key, string value)
        {
            _settings[key] = value;
            SaveSettings();
        }

        public void SaveSettings()
        {
            // Load the existing JSON configuration file
            var json = File.ReadAllText(_appsettingsFilePath);
            dynamic jsonObj = JsonConvert.DeserializeObject(json);
            // Update the PrinterEntity section with the new settings
            jsonObj["PrinterEntity"]["DefaultPrinter"] = _settings.ContainsKey("DefaultPrinter") ? _settings["DefaultPrinter"] : jsonObj["PrinterEntity"]["DefaultPrinter"];
            jsonObj["PrinterEntity"]["DefaultPaperSize"] = _settings.ContainsKey("DefaultPaperSize") ? _settings["DefaultPaperSize"] : jsonObj["PrinterEntity"]["DefaultPaperSize"];

            // Convert the updated JSON object back to a string
            string output = JsonConvert.SerializeObject(jsonObj, Formatting.Indented);
            // Write the updated JSON string back to the file
            File.WriteAllText(_appsettingsFilePath, output);
        }

        public object GetTabPage()
        {
            printForm.PopulateForm(); // Populate form elements

            return printForm.PrintTap; // Return the TabPage as an object
        }

        public bool PrintAsync(TransactionDto transactionDto)
        {
            try
            {
                // Log print job start
                _ = LogAsync($"Starting print job for transaction {transactionDto?.No ?? "Unknown"}", "INFO");

                // Get the printer settings from SettingsManager
                PrinterEntity printerSettings = GetPrinterEntity();
                string defaultPrinter = printerSettings.DefaultPrinter;
                string defaultPaperSize = printerSettings.DefaultPaperSize;

                // Set up the printer and paper size
                var paperSize = ConfigurePrinterSettings(defaultPrinter, defaultPaperSize);

                if (!string.IsNullOrEmpty(transactionDto?.HtmlContent))
                {
                    // Generate the PDF file from HTML content
                    string tempFilePath = GeneratePdfFile(transactionDto.HtmlContent, paperSize);

                    // Print the PDF file and delete after printing
                    bool result = PrintPdfAndMonitorJobAsync(tempFilePath, defaultPrinter, paperSize);

                    if (result)
                    {
                        _ = LogAsync($"Print job completed successfully for transaction {transactionDto.No}", "INFO");
                    }
                    else
                    {
                        _ = LogAsync($"Print job failed for transaction {transactionDto.No}", "ERROR");
                    }

                    return result;
                }
                else
                {
                    _ = LogAsync("Print job failed: Empty HTML content", "ERROR");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _ = LogAsync($"Print job exception for transaction {transactionDto?.No ?? "Unknown"}: {ex.Message}", "ERROR");
                return HandlePrintingError(ex);
            }
        }







        public void SetPrinter(string printerName)
        {
            PrintDocument.PrinterSettings.PrinterName = printerName;
        }

        public void SetPaperSize(PaperSize paperSize)
        {
            PrintDocument.DefaultPageSettings.PaperSize = paperSize;
        }
        public string GetDefaultPrinter()
        {
            // Create a PrinterSettings object which automatically picks up the default printer
            PrinterSettings settings = new PrinterSettings();

            // Return the name of the default printer
            return settings.PrinterName;
        }

        public IEnumerable<string> GetInstalledPrinters()
        {
            return PrinterSettings.InstalledPrinters.Cast<string>();
        }

        public PaperSize[] GetPaperSizes(string printerName)
        {
            using (var tempPrintDocument = new PrintDocument())
            {
                tempPrintDocument.PrinterSettings.PrinterName = printerName;
                return tempPrintDocument.PrinterSettings.PaperSizes.Cast<PaperSize>().ToArray();
            }
        }






        public bool PrintPdf(string pdfFilePath, string printerName, PaperSize paperSize)
        {
            try
            {
                using (var pdfDocument = PdfiumViewer.PdfDocument.Load(pdfFilePath))
                {
                    // Create a PrintDocument from PdfiumViewer
                    var printDocument = pdfDocument.CreatePrintDocument();
                    // Set page settings
                    printDocument.DefaultPageSettings = new PageSettings
                    {
                        PaperSize = paperSize,
                        //Landscape = true // Set to true for landscape orientation
                    };
                    printDocument.PrinterSettings.PrinterName = printerName;


                    // Print the document
                    printDocument.Print();

                    return true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error printing PDF: {ex.Message}");
                return false;
            }
        }







        private PaperSize ConfigurePrinterSettings(string printerName, string paperSizeName)
        {
            var paperSizes = GetPaperSizes(printerName).ToList();

            if (!string.IsNullOrEmpty(paperSizeName))
            {
                var selectedPaperSize = paperSizes.FirstOrDefault(p => p.PaperName.Equals(paperSizeName, StringComparison.OrdinalIgnoreCase));

                if (selectedPaperSize != null)
                {
                    SetPaperSize(selectedPaperSize);
                    return selectedPaperSize;
                }
                else
                {
                    Console.WriteLine("Default paper size not found. Using the first available paper size.");
                }
            }
            else
            {
                Console.WriteLine("Default paper size is not set in settings.");
            }

            // Fallback to the first available paper size if the specified one is not found
            var fallbackPaperSize = paperSizes.FirstOrDefault();
            if (fallbackPaperSize != null)
            {
                SetPaperSize(fallbackPaperSize);
            }

            return fallbackPaperSize;
        }

        private string GeneratePdfFile(string htmlContent, PaperSize paperSize)
        {
            string tempFilePath = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString() + ".pdf");

            IronPdf.License.LicenseKey = "IRONPDF-BOARD4ALL.BIZ-120981-6E5672-8683679C43-3138BC52-NEx-T24";

            var printOptions = new PdfPrintOptions
            {
                InputEncoding = Encoding.UTF8,
                FitToPaperWidth = true,
                Zoom = 400,
                MarginLeft = 0,
                MarginRight = 0,
                MarginTop = 0,
                MarginBottom = 10,
            };

            if (paperSize != null)
            {
                double widthInMm = paperSize.Width;
                double heightInMm = paperSize.Height;
                printOptions.SetCustomPaperSizeinMilimeters(widthInMm, heightInMm);
            }

            var renderer = new HtmlToPdf(printOptions);
            var pdfDocument = renderer.RenderHtmlAsPdf(htmlContent);
            File.WriteAllBytes(tempFilePath, pdfDocument.BinaryData);

            return tempFilePath;
        }

        private bool PrintPdfAndMonitorJobAsync(string filePath, string printerName, PaperSize paperSize)
        {
            bool printSuccess = false;

            try
            {
                printSuccess = PrintPdf(filePath, printerName, paperSize);

                if (printSuccess)
                {
                    return printJobMonitor.MonitorPrintJobAsync(printerName);
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                return false;
                throw; // Re-throw the exception after setting the task result
            }
            finally
            {
                // Ensure the file is deleted whether the print succeeds or fails
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
            }
        }

        private bool HandlePrintingError(Exception ex)
        {
            Console.WriteLine($"An error occurred during printing: {ex.Message}");
            return false;
        }



        public string GetBaseUrl()
        {
            return _configuration.GetSection("Server").GetValue<string>("BaseUrl");

        }

        public string GetCOMPort()
        {
            return _configuration.GetSection("DefaultCOMPort").GetValue<string>("COMPort");
        }

        public string GetBarcodeBaseUrl()
        {
            return _configuration.GetSection("Barcode").GetValue<string>("BarcodeBaseUrl");
        }






    }
}
