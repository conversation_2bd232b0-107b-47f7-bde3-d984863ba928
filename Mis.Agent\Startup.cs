﻿using Mis.Shared.Interface;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ApplicationParts;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using System.Drawing.Printing;
using System.Reflection;

namespace Mis.Agent
{
    public class Startup
    {
        public void ConfigureServices(IServiceCollection services)
        {
            // Enable CORS with an "AllowAll" policy
            services.AddCors(options =>
            {
                options.AddPolicy("AllowAll", builder =>
                    builder.AllowAnyOrigin()
                           .AllowAnyMethod()
                           .AllowAnyHeader());
            });

            // Add SignalR support
            services.AddSignalR();
            services.AddTransient<AgentForm>();
            // In your host builder or Startup.ConfigureServices:
            services.AddSingleton<SerialPortManager>();

            services.AddControllers();
            // Load plugin assemblies dynamically
            LoadPluginAssemblies(services);

            // Configure Swagger for API documentation
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "My API", Version = "v1" });
            });
        }
        private void LoadPluginAssemblies(IServiceCollection services)
        {
            var pluginsPath = Path.Combine(AppContext.BaseDirectory, "Plugins");

            if (Directory.Exists(pluginsPath))
            {
                var pluginAssemblies = Directory.GetFiles(pluginsPath, "*.dll")
                                                 .Select(Assembly.LoadFile)
                                                 .ToList();

                foreach (var assembly in pluginAssemblies)
                {
                    // Register controllers from the loaded assembly
                    services.AddControllers()
                            .AddApplicationPart(assembly)
                            .AddControllersAsServices();
                    // Register services and controllers
                    RegisterServicesFromAssembly(services, assembly);
                }
            }
        }
        private void RegisterServicesFromAssembly(IServiceCollection services, Assembly assembly)
        {
            try
            {
                foreach (var type in assembly.GetTypes())
                {
                    var barcodeAppServiceInterface = type.GetInterfaces()
                        .FirstOrDefault(i => i.Name == "IBarcodeAppService");

                    if (barcodeAppServiceInterface != null && !type.IsInterface && !type.IsAbstract)
                    {
                        services.AddSingleton(barcodeAppServiceInterface, type);
                        Console.WriteLine($"Registered IBarcodeAppService: {type.FullName}");
                    }

                    var scannerAppServiceInterface = type.GetInterfaces()
                        .FirstOrDefault(i => i.Name == "IScannerAppService");

                    if (scannerAppServiceInterface != null && !type.IsInterface && !type.IsAbstract)
                    {
                        services.AddSingleton(scannerAppServiceInterface, type);
                        Console.WriteLine($"Registered IScannerAppService: {type.FullName}");
                    }

                    var printAppServiceInterface = type.GetInterfaces()
                        .FirstOrDefault(i => i.Name == "IPrintAppService");

                    if (printAppServiceInterface != null && !type.IsInterface && !type.IsAbstract)
                    {
                        services.AddSingleton(printAppServiceInterface, type);
                        Console.WriteLine($"Registered IPrintAppService: {type.FullName}");
                    }


                    var notificationAppServiceInterface = type.GetInterfaces()
                        .FirstOrDefault(i => i.Name == "INotificationAppService");

                    if (notificationAppServiceInterface != null && !type.IsInterface && !type.IsAbstract)
                    {
                        services.AddSingleton(notificationAppServiceInterface, type);
                        Console.WriteLine($"Registered INotificationAppService: {type.FullName}");
                    }

                    // Register controllers
                    if (typeof(ControllerBase).IsAssignableFrom(type) && !type.IsAbstract)
                    {
                        services.AddControllers().AddApplicationPart(type.Assembly).AddControllersAsServices();
                        Console.WriteLine($"Registered Controller: {type.FullName}");
                    }
                }

            }
            catch (Exception ex)
            {

                throw;
            }
        }




        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            // Enable developer exception page for debugging
            app.UseDeveloperExceptionPage();

            // Serve static files
            app.UseStaticFiles();

            // Enable routing
            app.UseRouting();

            // Enable CORS
            app.UseCors("AllowAll");

            // Enable Authorization (if needed)
            app.UseAuthorization();
            // Configure endpoints
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers(); // Map controller routes
                endpoints.MapHub<ChatHub>("/chatHub"); // Map SignalR hub
            });

            // Enable Swagger
            app.UseSwagger();
            app.UseSwaggerUI(options =>
            {
                options.SwaggerEndpoint("/swagger/v1/swagger.json", "v1");
                options.RoutePrefix = string.Empty; // Swagger UI at root
            });
        }
    }
}
