using Mis.Shared.Interface;
using System.Diagnostics;
using System.Reflection;
using System.Windows.Forms;
using System.Xml.Linq;

namespace Mis.Agent.Port
{
    public partial class PortForm : Form
    {
        private IPortAppService _portAppService;
        public PortForm(IPortAppService portAppService)
        {
            _portAppService = portAppService;
            InitializeComponent();
            textBox3.Text = _portAppService.GetBaseUrl();
        }

        private void savePortConfiguration_Click(object sender, EventArgs e)
        {
            string newUrl = textBox3.Text;

            if (!string.IsNullOrWhiteSpace(newUrl))
            {
                try
                {
                    // Validate URL format
                    if (!IsValidUrl(newUrl))
                    {
                        MessageBox.Show("الرجاء إدخال رابط صحيح (مثال: http://localhost:5000)", "رابط غير صحيح",
                                      MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    var currentUrl = _portAppService.GetBaseUrl();

                    if (currentUrl != newUrl)
                    {
                        // Update the BaseUrl in appsettings.json
                        _portAppService.UpdateBaseUrl(newUrl);

                        // Confirm the update without restarting
                        MessageBox.Show("تم حفظ إعدادات المنفذ بنجاح. سيتم تطبيق التغييرات عند إعادة تشغيل التطبيق.",
                                      "تم الحفظ", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        MessageBox.Show("الرابط المدخل مطابق للرابط الحالي. لم يتم إجراء أي تغييرات.",
                                      "لا توجد تغييرات", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تحديث الرابط: {ex.Message}", "خطأ",
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            else
            {
                MessageBox.Show("الرجاء إدخال رابط صحيح.", "رابط غير صحيح",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private bool IsValidUrl(string url)
        {
            try
            {
                var uri = new Uri(url);
                return uri.Scheme == Uri.UriSchemeHttp || uri.Scheme == Uri.UriSchemeHttps;
            }
            catch
            {
                return false;
            }
        }


    }




}
