﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Mis.Agent.Print
{
    public static class ConfigPaths
    {
        // Compute the correct path to appsettings.json by navigating correctly
        public static readonly string AppSettingsPath = Path.Combine(
            Path.GetFullPath(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, @"..\..\..\misc\Mis.Agent")), // Navigate to the correct directory
            "appsettings.json" // Configuration file name
        );

      
    }

}
