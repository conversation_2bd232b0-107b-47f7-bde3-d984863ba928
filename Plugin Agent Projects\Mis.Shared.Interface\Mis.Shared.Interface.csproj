﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>net9.0;net9.0-windows</TargetFrameworks>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AssemblyName>MIS.Shared.Interface</AssemblyName>
    <RootNamespace>MIS.Shared.Interface</RootNamespace>
    <Version>1.1.0</Version>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Product>MIS Agent System</Product>
    <Company>MIS Company</Company>
    <Copyright>Copyright © MIS Company 2024</Copyright>
    <Description>MIS Shared Interface - Plugin Architecture Interfaces</Description>
  </PropertyGroup>


  <ItemGroup>
    <PackageReference Include="System.Drawing.Common" Version="9.0.6" />
    <PackageReference Include="System.IO.Ports" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="9.0.6" />
    <PackageReference Include="System.Data.SQLite" Version="1.0.119" />


  </ItemGroup>

  <ItemGroup>
    <Reference Include="Interop.WIA">
      <HintPath>WIA\Interop.WIA.dll</HintPath>
    </Reference>
  </ItemGroup>
</Project>