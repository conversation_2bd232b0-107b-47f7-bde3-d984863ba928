﻿using System;
using System.Runtime.InteropServices;

namespace Mis.Agent.Print
{
    public class PrintJobMonitor
    {
        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Unicode)]
        public struct JOB_INFO_1
        {
            public uint JobId;
            public string pPrinterName;
            public string pMachineName;
            public string pUserName;
            public string pDocument;
            public string pDatatype;
            public string pStatus;
            public uint Status;
            public uint Priority;
            public uint Position;
            public uint TotalPages;
            public uint PagesPrinted;
            public SYSTEMTIME Submitted;
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct SYSTEMTIME
        {
            public ushort wYear;
            public ushort wMonth;
            public ushort wDayOfWeek;
            public ushort wDay;
            public ushort wHour;
            public ushort wMinute;
            public ushort wSecond;
            public ushort wMilliseconds;
        }

        [DllImport("winspool.drv", CharSet = CharSet.Unicode, SetLastError = true)]
        public static extern bool EnumJobs(IntPtr hPrinter, uint FirstJob, uint NoJobs, uint Level, IntPtr pJob, uint cbBuf, ref uint pcbNeeded, ref uint pcReturned);

        [DllImport("winspool.drv", CharSet = CharSet.Unicode, SetLastError = true)]
        public static extern bool OpenPrinter(string pPrinterName, out IntPtr phPrinter, IntPtr pDefault);

        [DllImport("winspool.drv", CharSet = CharSet.Unicode, SetLastError = true)]
        public static extern bool ClosePrinter(IntPtr hPrinter);

        public static JOB_INFO_1[] GetJobs(string printerName)
        {
            IntPtr hPrinter;
            if (OpenPrinter(printerName, out hPrinter, IntPtr.Zero))
            {
                uint bytesNeeded = 0;
                uint jobsReturned = 0;

                // First call to determine buffer size
                EnumJobs(hPrinter, 0, 255, 1, IntPtr.Zero, 0, ref bytesNeeded, ref jobsReturned);

                IntPtr pJobInfo = Marshal.AllocHGlobal((int)bytesNeeded);

                JOB_INFO_1[] jobs = null;
                if (EnumJobs(hPrinter, 0, 255, 1, pJobInfo, bytesNeeded, ref bytesNeeded, ref jobsReturned))
                {
                    jobs = new JOB_INFO_1[jobsReturned];
                    IntPtr currentJob = pJobInfo;

                    for (int i = 0; i < jobsReturned; i++)
                    {
                        jobs[i] = Marshal.PtrToStructure<JOB_INFO_1>(currentJob);
                        currentJob = IntPtr.Add(currentJob, Marshal.SizeOf(typeof(JOB_INFO_1)));
                    }
                }

                Marshal.FreeHGlobal(pJobInfo);
                ClosePrinter(hPrinter);

                return jobs;
            }

            return null;
        }



        public bool MonitorPrintJobAsync(string printerName)
        {
            try
            {
                bool jobCompleted = false;
                for (int i = 0; i < 5; i++) // Check status up to 5 times
                {
                    var jobs = GetJobs(printerName);
                    if (jobs != null)
                    {
                        foreach (var job in jobs)
                        {
                            // Example status flag; adjust as needed
                            if ((job.Status) == 0) // Check if the job is completed
                            {
                                jobCompleted = false;
                            }
                            else
                            {
                                jobCompleted = true;
                            }
                        }
                    }
                }
                return jobCompleted;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"An error occurred while monitoring the print job: {ex.Message}");
                return false;
            }
        }



    }
}
