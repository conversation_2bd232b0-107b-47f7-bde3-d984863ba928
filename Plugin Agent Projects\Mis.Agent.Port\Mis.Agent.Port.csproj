﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <EnableWindowsTargeting>true</EnableWindowsTargeting>
    <AssemblyName>MIS.Agent.Port</AssemblyName>
    <RootNamespace>MIS.Agent.Port</RootNamespace>
    <Version>1.0.2</Version>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Product>MIS Agent System</Product>
    <Company>MIS Company</Company>
    <Copyright>Copyright © MIS Company 2024</Copyright>
    <Description>MIS Agent Port Plugin - Serial Port Management Services</Description>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Owin" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.6" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.8.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.6" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.Async" Version="2.1.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="9.0.6" />
    <PackageReference Include="Microsoft.Windows.Compatibility" Version="9.0.6" />
    <PackageReference Include="System.Data.SQLite" Version="1.0.119" />
    <PackageReference Include="System.Security.Cryptography.Pkcs" Version="9.0.6" />
    <PackageReference Include="System.Security.Cryptography.X509Certificates" Version="4.3.2" />
    <PackageReference Include="Volo.Abp.Autofac" Version="9.0.6" />
  </ItemGroup>


  <ItemGroup>
    <ProjectReference Include="..\Mis.Shared.Interface\Mis.Shared.Interface.csproj" />
  </ItemGroup>

  <!-- Digital Signing Target -->
  <Target Name="PostBuildSigning" AfterTargets="Build" Condition="'$(Configuration)' == 'Release'">
    <PropertyGroup>
      <SignToolPath>C:\Program Files (x86)\Windows Kits\10\bin\10.0.26100.0\x64\signtool.exe</SignToolPath>
      <CertificatePath>$(ProjectDir)..\..\Mis.Agent\MisCodeSign.pfx</CertificatePath>
      <CertificatePassword>MisCompanyPassword123!</CertificatePassword>
    </PropertyGroup>

    <Exec
      Command='"$(SignToolPath)" sign /f "$(CertificatePath)" /p "$(CertificatePassword)" /tr http://timestamp.digicert.com /td sha256 /fd sha256 "$(TargetPath)"'
      Condition="Exists('$(CertificatePath)')"
      ContinueOnError="true" />
  </Target>
</Project>