using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Mis.Agent.Print
{
    /// <summary>
    /// Form for viewing application logs
    /// </summary>
    public partial class LogsViewerForm : Form
    {
        private DataGridView logsDataGridView;
        private Button refreshButton;
        private Button clearLogsButton;
        private ComboBox levelFilterComboBox;
        private Label statusLabel;
        private NotificationsService notificationsService;

        public LogsViewerForm()
        {
            InitializeComponent();
            notificationsService = new NotificationsService();
            LoadLogs();
        }

        private void InitializeComponent()
        {
            this.Text = "Application Logs Viewer";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;

            // Create controls
            var panel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 40,
                Padding = new Padding(10, 5, 10, 5)
            };

            refreshButton = new Button
            {
                Text = "Refresh",
                Size = new Size(80, 30),
                Location = new Point(10, 5)
            };
            refreshButton.Click += RefreshButton_Click;

            clearLogsButton = new Button
            {
                Text = "Clear Logs",
                Size = new Size(80, 30),
                Location = new Point(100, 5)
            };
            clearLogsButton.Click += ClearLogsButton_Click;

            levelFilterComboBox = new ComboBox
            {
                Size = new Size(100, 30),
                Location = new Point(190, 5),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            levelFilterComboBox.Items.AddRange(new[] { "All", "INFO", "ERROR", "WARNING" });
            levelFilterComboBox.SelectedIndex = 0;
            levelFilterComboBox.SelectedIndexChanged += LevelFilterComboBox_SelectedIndexChanged;

            statusLabel = new Label
            {
                Text = "Ready",
                Location = new Point(300, 10),
                AutoSize = true
            };

            panel.Controls.AddRange(new Control[] { refreshButton, clearLogsButton, levelFilterComboBox, statusLabel });

            // Create DataGridView
            logsDataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect
            };

            // Add columns
            logsDataGridView.Columns.Add("Id", "ID");
            logsDataGridView.Columns.Add("Level", "Level");
            logsDataGridView.Columns.Add("Message", "Message");
            logsDataGridView.Columns.Add("CreatedDate", "Date/Time");

            // Set column widths
            logsDataGridView.Columns["Id"].Width = 60;
            logsDataGridView.Columns["Level"].Width = 80;
            logsDataGridView.Columns["Message"].Width = 400;
            logsDataGridView.Columns["CreatedDate"].Width = 150;

            this.Controls.Add(logsDataGridView);
            this.Controls.Add(panel);
        }

        private async void RefreshButton_Click(object sender, EventArgs e)
        {
            await LoadLogs();
        }

        private async void ClearLogsButton_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show(
                "Are you sure you want to clear all logs? This action cannot be undone.",
                "Confirm Clear Logs",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning);

            if (result == DialogResult.Yes)
            {
                await ClearAllLogs();
                await LoadLogs();
            }
        }

        private async void LevelFilterComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            await LoadLogs();
        }

        private async Task LoadLogs()
        {
            try
            {
                statusLabel.Text = "Loading logs...";
                refreshButton.Enabled = false;

                var logs = await notificationsService.GetRecentLogsAsync(1000);

                // Apply level filter
                var selectedLevel = levelFilterComboBox.SelectedItem?.ToString();
                if (!string.IsNullOrEmpty(selectedLevel) && selectedLevel != "All")
                {
                    logs = logs.Where(l => l.Level == selectedLevel).ToList();
                }

                // Clear existing rows
                logsDataGridView.Rows.Clear();

                // Add logs to grid
                foreach (var log in logs)
                {
                    var row = logsDataGridView.Rows.Add();
                    logsDataGridView.Rows[row].Cells["Id"].Value = log.Id;
                    logsDataGridView.Rows[row].Cells["Level"].Value = log.Level;
                    logsDataGridView.Rows[row].Cells["Message"].Value = log.Message;
                    logsDataGridView.Rows[row].Cells["CreatedDate"].Value = log.CreatedDate.ToString("yyyy-MM-dd HH:mm:ss");

                    // Color code by level
                    switch (log.Level)
                    {
                        case "ERROR":
                            logsDataGridView.Rows[row].DefaultCellStyle.BackColor = Color.LightPink;
                            break;
                        case "WARNING":
                            logsDataGridView.Rows[row].DefaultCellStyle.BackColor = Color.LightYellow;
                            break;
                        case "INFO":
                            logsDataGridView.Rows[row].DefaultCellStyle.BackColor = Color.LightGreen;
                            break;
                    }
                }

                statusLabel.Text = $"Loaded {logs.Count} log entries";
            }
            catch (Exception ex)
            {
                statusLabel.Text = "Error loading logs";
                MessageBox.Show($"Error loading logs: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                refreshButton.Enabled = true;
            }
        }

        private async Task ClearAllLogs()
        {
            try
            {
                statusLabel.Text = "Clearing logs...";
                clearLogsButton.Enabled = false;

                await notificationsService.ClearAllLogsAsync();

                statusLabel.Text = "Logs cleared successfully";
                MessageBox.Show("All logs have been cleared successfully.", "Logs Cleared",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                statusLabel.Text = "Error clearing logs";
                MessageBox.Show($"Error clearing logs: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                clearLogsButton.Enabled = true;
            }
        }
    }
}
