$ErrorActionPreference = "Stop"

# CONFIGURATION
$pfxPassword = "MisCompanyPassword123!"
$subjectName = "CN=MyCompany Code Signing"
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path

# ابحث عن أول ملف بلاحقة .pfx
$pfxFile = Get-ChildItem -Path $scriptDir -Filter *.pfx | Select-Object -First 1
if ($pfxFile) {
    $pfxPath = $pfxFile.FullName
    Write-Host "Found PFX file: $pfxPath"
}
else {
    $pfxPath = Join-Path $scriptDir "MisCodeSign.pfx"
    Write-Host "No PFX file found. Will create new at: $pfxPath"
}

# ابحث عن أول ملف بلاحقة .cer
$cerFile = Get-ChildItem -Path $scriptDir -Filter *.cer | Select-Object -First 1
if ($cerFile) {
    $cerPath = $cerFile.FullName
    Write-Host "Found CER file: $cerPath"
}
else {
    $cerPath = Join-Path $scriptDir "MisCompanyCert.cer"
    Write-Host "No CER file found. Will create new at: $cerPath"
}

$publishOutput = "C:\Deploy\MisAgent"
$projectName = "Mis.Agent"
$signtool = "C:\Program Files (x86)\Windows Kits\10\bin\10.0.26100.0\x64\signtool.exe"
Test-Path $signtool



# تحقق من وجود ملف .pfx
if (!(Test-Path $pfxPath)) {
    Write-Host "Certificate file not found. Creating self-signed certificate..."

    # إنشاء شهادة جديدة
    $cert = New-SelfSignedCertificate `
        -Type CodeSigningCert `
        -Subject $subjectName `
        -KeyExportPolicy Exportable `
        -KeySpec Signature `
        -KeyLength 2048 `
        -HashAlgorithm sha256 `
        -CertStoreLocation "Cert:\CurrentUser\My"

    $securePwd = ConvertTo-SecureString -String $pfxPassword -Force -AsPlainText

    # إنشاء المجلد إذا لم يكن موجودًا
    $certDir = Split-Path $pfxPath
    if (!(Test-Path $certDir)) {
        New-Item -ItemType Directory -Path $certDir | Out-Null
    }

    # تصدير ملف .pfx و .cer
    Export-PfxCertificate -Cert $cert -FilePath $pfxPath -Password $securePwd
    Export-Certificate -Cert $cert -FilePath $cerPath

    # استيراد شهادة .cer الى مخزن Root الخاص بالنظام (LocalMachine)
    Import-Certificate -FilePath $cerPath -CertStoreLocation "Cert:\LocalMachine\Root" | Out-Null

    Write-Host "Certificate created, exported and trusted."
}
else {
    Write-Host "Using existing certificate: $pfxPath"
}

# إنشاء مجلد النشر إذا لم يكن موجودًا
if (!(Test-Path $publishOutput)) {
    Write-Host "Creating publish folder: $publishOutput"
    New-Item -ItemType Directory -Path $publishOutput | Out-Null
}

# تحديد ملفات exe و dll للتوقيع
$exePath = Join-Path $publishOutput "$projectName.exe"
$dllPath = Join-Path $publishOutput "$projectName.dll"

$filesToSign = @()
if (Test-Path $exePath) { $filesToSign += $exePath }
if (Test-Path $dllPath) { $filesToSign += $dllPath }

# إضافة جميع ملفات DLL في مجلد Plugins
$pluginsPath = Join-Path $publishOutput "Plugins"
if (Test-Path $pluginsPath) {
    $pluginDlls = Get-ChildItem -Path $pluginsPath -Filter "MIS.*.dll" -Recurse
    foreach ($dll in $pluginDlls) {
        $filesToSign += $dll.FullName
        Write-Host "Found plugin DLL to sign: $($dll.Name)"
    }
}

# إضافة ملفات DLL الأساسية الأخرى
$coreFiles = @(
    "MIS.Shared.Interface.dll"
)

foreach ($coreFile in $coreFiles) {
    $corePath = Join-Path $publishOutput $coreFile
    if (Test-Path $corePath) {
        $filesToSign += $corePath
        Write-Host "Found core DLL to sign: $coreFile"
    }
}

if (-not $filesToSign) {
    Write-Host "No target files found to sign."
    exit 0
}

# توقيع الملفات
foreach ($file in $filesToSign) {
    Write-Host "Signing file: $file"

    # استدعاء signtool مباشرة مع المعاملات الصحيحة
    & $signtool sign /f $pfxPath /p $pfxPassword /tr http://timestamp.digicert.com /td sha256 /fd sha256 $file

    # التحقق من التوقيع
    $sig = Get-AuthenticodeSignature $file
    if ($sig.Status -eq "Valid") {
        Write-Host "File signed successfully: $(Split-Path $file -Leaf)"
    }
    else {
        Write-Host "Signing failed for $(Split-Path $file -Leaf): $($sig.StatusMessage)"
    }
}
