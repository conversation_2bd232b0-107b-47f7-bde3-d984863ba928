﻿using Mis.Shared.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Mis.Agent.Barcode
{
    public interface IScannerAppService: IPlugin , IConfigurablePlugin, ISerialPortPlugin, IBarcodePlugin
    {
        string GetSetting(string key);
        List<string> GetAvailableScanners();
        void SaveScannerConfiguration(string scannerName, bool isScanByBarcodeReader);

    }
}
