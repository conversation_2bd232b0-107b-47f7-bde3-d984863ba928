﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Mis.Agent.Port
{
    public static class PortConfigPaths
    {
        // Compute the correct path to appsettings.json by navigating correctly
        public static readonly string AppSettingsPath =
            Path.Combine(Path.GetFullPath(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, @"..\..\..\..\Mis.Agent\")), // Navigate to the correct directory
            "appsettings.json" // Configuration file name
        );

        // Compute the correct path to appsettings.json by navigating correctly
        public static readonly string NotificationsDataBasePath =
            Path.Combine(Path.GetFullPath(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, @"..\..\..\..\Mis.Agent\")), // Navigate to the correct directory
            "notifications.db" // Configuration file name
        );
    }


}
