using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Mis.Agent.Resources;
using System;
using System.Data.SQLite;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace MIS.Agent.Services
{
    /// <summary>
    /// Background service for automatic SQLite database cleanup
    /// </summary>
    public class DatabaseCleanupService : BackgroundService
    {
        private readonly ILogger<DatabaseCleanupService> _logger;
        private readonly TimeSpan _cleanupInterval = TimeSpan.FromHours(24); // Run daily
        private readonly int _maxLogRecords = 10000; // Keep maximum 10,000 log records
        private readonly int _maxLogDays = 30; // Keep logs for 30 days

        public DatabaseCleanupService(ILogger<DatabaseCleanupService> logger)
        {
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await PerformCleanup();
                    await Task.Delay(_cleanupInterval, stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    // Expected when cancellation is requested
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error during database cleanup");
                    await Task.Delay(TimeSpan.FromHours(1), stoppingToken); // Retry after 1 hour on error
                }
            }
        }

        /// <summary>
        /// Perform database cleanup operations
        /// </summary>
        public async Task PerformCleanup()
        {
            try
            {
                var dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "notifications.db");
                if (!File.Exists(dbPath))
                {
                    _logger.LogInformation("Database file not found, skipping cleanup");
                    return;
                }

                using var connection = new SQLiteConnection($"Data Source={dbPath}");
                await connection.OpenAsync();

                // Clean up old log records by date
                await CleanupByDate(connection);

                // Clean up excess log records by count
                await CleanupByCount(connection);

                // Vacuum database to reclaim space
                await VacuumDatabase(connection);

                _logger.LogInformation("Database cleanup completed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during database cleanup operation");
                throw;
            }
        }

        /// <summary>
        /// Remove log records older than specified days
        /// </summary>
        private async Task CleanupByDate(SQLiteConnection connection)
        {
            var cutoffDate = DateTime.Now.AddDays(-_maxLogDays);
            var query = @"
                DELETE FROM Logs 
                WHERE CreatedDate < @CutoffDate";

            using var command = new SQLiteCommand(query, connection);
            command.Parameters.AddWithValue("@CutoffDate", cutoffDate);

            var deletedRows = await command.ExecuteNonQueryAsync();
            if (deletedRows > 0)
            {
                _logger.LogInformation($"Deleted {deletedRows} old log records (older than {_maxLogDays} days)");
            }
        }

        /// <summary>
        /// Keep only the most recent log records up to the maximum count
        /// </summary>
        private async Task CleanupByCount(SQLiteConnection connection)
        {
            // First, check if we have more records than the limit
            var countQuery = "SELECT COUNT(*) FROM Logs";
            using var countCommand = new SQLiteCommand(countQuery, connection);
            var totalRecords = Convert.ToInt32(await countCommand.ExecuteScalarAsync());

            if (totalRecords <= _maxLogRecords)
            {
                return; // No cleanup needed
            }

            // Delete oldest records, keeping only the most recent ones
            var deleteQuery = @"
                DELETE FROM Logs 
                WHERE Id NOT IN (
                    SELECT Id FROM Logs 
                    ORDER BY CreatedDate DESC 
                    LIMIT @MaxRecords
                )";

            using var deleteCommand = new SQLiteCommand(deleteQuery, connection);
            deleteCommand.Parameters.AddWithValue("@MaxRecords", _maxLogRecords);

            var deletedRows = await deleteCommand.ExecuteNonQueryAsync();
            if (deletedRows > 0)
            {
                _logger.LogInformation($"Deleted {deletedRows} excess log records (keeping {_maxLogRecords} most recent)");
            }
        }

        /// <summary>
        /// Vacuum database to reclaim space after deletions
        /// </summary>
        private async Task VacuumDatabase(SQLiteConnection connection)
        {
            try
            {
                using var command = new SQLiteCommand("VACUUM", connection);
                await command.ExecuteNonQueryAsync();
                _logger.LogInformation("Database vacuum completed");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to vacuum database");
            }
        }

        /// <summary>
        /// Manually trigger cleanup (for testing or immediate cleanup)
        /// </summary>
        public static async Task TriggerCleanup(ILogger logger = null)
        {
            try
            {
                var typedLogger = (ILogger<DatabaseCleanupService>)(logger ?? NullLogger<DatabaseCleanupService>.Instance);
                var service = new DatabaseCleanupService(typedLogger);
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "Manual database cleanup failed");
                throw;
            }
        }

        /// <summary>
        /// Get database statistics
        /// </summary>
        public static async Task<DatabaseStats> GetDatabaseStats()
        {
            var dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "notifications.db");
            if (!File.Exists(dbPath))
            {
                return new DatabaseStats { FileExists = false };
            }

            var fileInfo = new FileInfo(dbPath);
            var stats = new DatabaseStats
            {
                FileExists = true,
                FileSizeBytes = fileInfo.Length,
                LastModified = fileInfo.LastWriteTime
            };

            try
            {
                using var connection = new SQLiteConnection($"Data Source={dbPath}");
                await connection.OpenAsync();

                // Get record count
                using var countCommand = new SQLiteCommand("SELECT COUNT(*) FROM Logs", connection);
                stats.RecordCount = Convert.ToInt32(await countCommand.ExecuteScalarAsync());

                // Get oldest record date
                using var oldestCommand = new SQLiteCommand("SELECT MIN(CreatedDate) FROM Logs", connection);
                var oldestResult = await oldestCommand.ExecuteScalarAsync();
                if (oldestResult != DBNull.Value && oldestResult != null)
                {
                    stats.OldestRecordDate = Convert.ToDateTime(oldestResult);
                }
            }
            catch (Exception ex)
            {
                stats.Error = ex.Message;
            }

            return stats;
        }
    }

    /// <summary>
    /// Database statistics information
    /// </summary>
    public class DatabaseStats
    {
        public bool FileExists { get; set; }
        public long FileSizeBytes { get; set; }
        public DateTime LastModified { get; set; }
        public int RecordCount { get; set; }
        public DateTime? OldestRecordDate { get; set; }
        public string Error { get; set; }

        public string FileSizeFormatted => FileExists ? FormatBytes(FileSizeBytes) : "N/A";

        private static string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB" };
            int counter = 0;
            decimal number = bytes;
            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }
            return $"{number:n1} {suffixes[counter]}";
        }
    }
}
