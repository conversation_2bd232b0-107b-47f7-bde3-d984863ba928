﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>

    <EnableWindowsTargeting>true</EnableWindowsTargeting>

  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Owin" Version="9.0.6"  />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.6" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.8.0" />
    <PackageReference Include="Volo.Abp.Core" Version="7.3.2" />
    <PackageReference Include="System.IO.Ports" Version="9.0.6" />
    <PackageReference Include="System.Management" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="9.0.6" />

    <ProjectReference Include="..\..\..\..\shared\Mis.Shared.Interface\Mis.Shared.Interface.csproj" />

  </ItemGroup>


  <ItemGroup>
    <Reference Include="Interop.WIA">
      <HintPath>WIA\Interop.WIA.dll</HintPath>
    </Reference>
  </ItemGroup>
</Project>