﻿using Mis.Shared.Interface;
using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Linq;
using System.Management;
using System.Text;
using System.Threading.Tasks;

namespace Mis.Agent.Barcode
{
    public interface IBarcodeAppService :IPlugin /*IPublicBarcodeAppService*/
    {
        void PopulateCOMPorts();
        void PublicInitialize(string baseUrl, string comPort, bool isCaptureImageMode);

        void SetBarcodeScannerPort();
        string[] GetAvailableCOMPorts();
        string GetConnectedBarcodePort();
        bool IsBarcodeScannerConnected(string portName);
        void OnDeviceChanged(object sender, EventArrivedEventArgs e);
        void SendManaualData();
        void UpdateBarcodeBaseUrl(string newBaseUrl);
        void UpdateCOMPort(string selectedComPort);
        bool IsValidBarcodeUrl(string url);


    }
}
