using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using Microsoft.Extensions.DependencyInjection;

namespace Mis.Shared.Interface
{
    public class PluginManager
    {
        private readonly List<PluginMetadata> _plugins = new List<PluginMetadata>();
        private readonly IServiceProvider _serviceProvider;

        public PluginManager(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public IReadOnlyList<PluginMetadata> LoadedPlugins => _plugins.AsReadOnly();

        public void LoadPlugins(string pluginDirectory)
        {
            if (!Directory.Exists(pluginDirectory))
            {
                Console.WriteLine($"Plugin directory not found: {pluginDirectory}");
                return;
            }

            var pluginFiles = Directory.GetFiles(pluginDirectory, "*.dll");
            
            foreach (var file in pluginFiles)
            {
                try
                {
                    LoadPluginFromAssembly(file);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Failed to load plugin from {file}: {ex.Message}");
                }
            }
        }

        private void LoadPluginFromAssembly(string assemblyPath)
        {
            var assembly = Assembly.LoadFrom(assemblyPath);
            Type[] types;

            try
            {
                types = assembly.GetTypes();
            }
            catch (ReflectionTypeLoadException ex)
            {
                types = ex.Types.Where(t => t != null).ToArray();
                foreach (var loaderException in ex.LoaderExceptions)
                {
                    Console.WriteLine($"Error loading type: {loaderException?.Message}");
                }
            }

            foreach (var type in types)
            {
                if (typeof(IPlugin).IsAssignableFrom(type) && !type.IsAbstract && !type.IsInterface)
                {
                    var metadata = CreatePluginMetadata(type);
                    _plugins.Add(metadata);
                    Console.WriteLine($"Loaded plugin: {metadata.Name} v{metadata.Version}");
                }
            }
        }

        private PluginMetadata CreatePluginMetadata(Type pluginType)
        {
            var metadata = new PluginMetadata
            {
                PluginType = pluginType,
                SupportedInterfaces = new List<Type>()
            };

            // Try to get plugin info from instance (if possible)
            try
            {
                var instance = ActivatorUtilities.CreateInstance(_serviceProvider, pluginType) as IPlugin;
                if (instance != null)
                {
                    metadata.Name = instance.PluginName;
                    metadata.Version = instance.PluginVersion;
                }
            }
            catch
            {
                // Fallback to type name if instantiation fails
                metadata.Name = pluginType.Name;
                metadata.Version = "Unknown";
            }

            // Check for optional interfaces
            if (typeof(IConfigurablePlugin).IsAssignableFrom(pluginType))
                metadata.SupportedInterfaces.Add(typeof(IConfigurablePlugin));
            
            if (typeof(ISerialPortPlugin).IsAssignableFrom(pluginType))
                metadata.SupportedInterfaces.Add(typeof(ISerialPortPlugin));
            
            if (typeof(IBarcodePlugin).IsAssignableFrom(pluginType))
                metadata.SupportedInterfaces.Add(typeof(IBarcodePlugin));

            return metadata;
        }

        public T GetPlugin<T>() where T : class, IPlugin
        {
            var pluginMetadata = _plugins.FirstOrDefault(p => typeof(T).IsAssignableFrom(p.PluginType));
            if (pluginMetadata == null)
                return null;

            return ActivatorUtilities.CreateInstance(_serviceProvider, pluginMetadata.PluginType) as T;
        }

        public IEnumerable<IPlugin> GetAllPlugins()
        {
            foreach (var metadata in _plugins)
            {
                yield return ActivatorUtilities.CreateInstance(_serviceProvider, metadata.PluginType) as IPlugin;
            }
        }

        public IEnumerable<T> GetPluginsWithInterface<T>() where T : class
        {
            var interfaceType = typeof(T);
            var compatiblePlugins = _plugins.Where(p => interfaceType.IsAssignableFrom(p.PluginType));

            foreach (var metadata in compatiblePlugins)
            {
                var instance = ActivatorUtilities.CreateInstance(_serviceProvider, metadata.PluginType);
                if (instance is T typedInstance)
                    yield return typedInstance;
            }
        }

        public bool HasPluginWithInterface<T>()
        {
            return _plugins.Any(p => typeof(T).IsAssignableFrom(p.PluginType));
        }
    }
}
