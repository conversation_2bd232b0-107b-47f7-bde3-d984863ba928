using System.Drawing.Printing;
using System.Runtime.InteropServices;

namespace Mis.Agent.Print
{
    public partial class PrintForm : Form
    {
        private IPrintAppService _printAppService { get; set; }
        private string _previousPrinter;

        public PrintForm(IPrintAppService printAppService)
        {
            _printAppService = printAppService;
            InitializeComponent();
            LoadPrinterSettings();
        }

        private void LoadPrinterSettings()
        {
            PrinterEntity printerSettings = _printAppService.GetPrinterEntity();
            textBox1.Text = printerSettings.DefaultPrinter;
            textBox2.Text = printerSettings.DefaultPaperSize;
            PopulatePrinterList();
        }
        public void PopulateForm()
        {
            LoadPrinterSettings();  // Load settings or populate other fields as needed
            PopulatePrinterList();  // Populate printer and paper size lists
        }
        private void PopulatePrinterList()
        {
            printerComboBox.Items.Clear();
            foreach (string printer in _printAppService.GetInstalledPrinters())
            {
                printerComboBox.Items.Add(printer);
            }

            if (printerComboBox.Items.Count > 0)
            {
                printerComboBox.SelectedIndex = 0;
                _previousPrinter = printerComboBox.SelectedItem.ToString();
                PopulatePaperSizes();
            }
        }

        private void PopulatePaperSizes()
        {
            if (printerComboBox.SelectedItem == null) return;

            paperSizeComboBox.Items.Clear();
            string selectedPrinter = printerComboBox.SelectedItem.ToString();

            foreach (PaperSize paperSize in _printAppService.GetPaperSizes(selectedPrinter))
            {
                paperSizeComboBox.Items.Add(paperSize.PaperName);
            }

            if (paperSizeComboBox.Items.Count > 0)
            {
                paperSizeComboBox.SelectedIndex = 0;
            }
        }

        private void printerComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            string selectedPrinter = printerComboBox.SelectedItem?.ToString();

            if (selectedPrinter != _previousPrinter)
            {
                _previousPrinter = selectedPrinter;
                PopulatePaperSizes();
                ApplyPrintSettings();
            }
        }

        private void paperSizeComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyPrintSettings();
        }

        public void ApplyPrintSettings()
        {
            if (printerComboBox.SelectedItem != null)
            {
                _printAppService.SetPrinter(printerComboBox.SelectedItem.ToString());
            }
            else
            {
                _printAppService.ShowNotification("Print Notification", "No printer selected.");
                return;
            }

            if (paperSizeComboBox.SelectedItem != null)
            {
                PaperSize selectedPaperSize = null;
                foreach (PaperSize paperSize in _printAppService.GetPaperSizes(printerComboBox.SelectedItem.ToString()))
                {
                    if (paperSize.PaperName == paperSizeComboBox.SelectedItem.ToString())
                    {
                        selectedPaperSize = paperSize;
                        break;
                    }
                }

                if (selectedPaperSize != null)
                {
                    _printAppService.SetPaperSize(selectedPaperSize);
                }
                else
                {
                    _printAppService.ShowNotification("Print Notification", "Selected paper size is not available for the selected printer.");
                }
            }
            else
            {
                _printAppService.ShowNotification("Print Notification", "No paper size selected.");
            }
        }







        [DllImport("printui.dll", CharSet = CharSet.Unicode, SetLastError = true)]
        public static extern int PrintUIEntry(IntPtr hwnd, IntPtr hinst, string lpszCmdLine, int nCmdShow);

        [DllImport("winspool.drv", CharSet = CharSet.Auto, SetLastError = true)]
        public static extern int DocumentProperties(IntPtr hwnd, IntPtr hPrinter, string pDeviceName, IntPtr pDevModeOutput, IntPtr pDevModeInput, int fMode);

        [DllImport("winspool.drv", CharSet = CharSet.Auto, SetLastError = true)]
        public static extern bool OpenPrinter(string pPrinterName, out IntPtr phPrinter, IntPtr pDefault);

        [DllImport("winspool.drv", CharSet = CharSet.Auto, SetLastError = true)]
        public static extern bool ClosePrinter(IntPtr hPrinter);
        [DllImport("winspool.drv", CharSet = CharSet.Auto, SetLastError = true)]
        public static extern bool SetDefaultPrinter(string Name);
        [DllImport("winspool.drv", CharSet = CharSet.Auto, SetLastError = true)]
        public static extern int DeviceCapabilities(string pDevice, string pPort, int fwCapability, IntPtr pOutput, IntPtr pDevMode);

        private const int DC_PAPERNAMES = 16;
        private const int DC_PAPERS = 2;

        private void button1_ClickAsync(object sender, EventArgs e)
        {
            SaveDefaultPrinterAndPaperSize();
        }

        private void SaveDefaultPrinterAndPaperSize()
        {
            try
            {
                if (printerComboBox.SelectedItem != null)
                {
                    SetDefaultPrinter(printerComboBox.SelectedItem.ToString());
                    _printAppService.UpdateSetting("DefaultPrinter", printerComboBox.SelectedItem.ToString());

                }
                if (printerComboBox.SelectedItem != null && paperSizeComboBox.SelectedItem != null)
                {
                    SavePaperSize(printerComboBox.SelectedItem.ToString(), paperSizeComboBox.SelectedItem.ToString());
                }
                _printAppService.ShowNotification("Settings Saved", "Printer set as default. Note: Paper size settings may not apply universally.");
            }
            catch (Exception ex)
            {
                _printAppService.ShowNotification("Error", $"Failed to save default settings: {ex.Message}");
            }
        }

        private void SavePaperSize(string printerName, string paperSizeName)
        {
            _printAppService.UpdateSetting("DefaultPrinter", printerName);
            _printAppService.UpdateSetting("DefaultPaperSize", paperSizeName);

            // Update the UI components with the new settings
            textBox1.Text = printerName;
            textBox2.Text = paperSizeName;

            // Optional: Refresh any specific controls or the entire form
            this.Refresh();
        }


        private void button2_Click(object sender, EventArgs e)
        {
            ShowPrinterProperties(printerComboBox.SelectedItem.ToString());
        }

        private void ShowPrinterProperties(string printerName)
        {
            try
            {
                string arguments = $" /p /n \"{printerName}\"";
                int result = PrintUIEntry(IntPtr.Zero, IntPtr.Zero, arguments, 0);
                if (result != 0)
                {
                    _printAppService.ShowNotification("Printer Notification", "Failed to show printer properties.");
                }
            }
            catch (Exception ex)
            {
                _printAppService.ShowNotification("Printer Notification", $"Error showing printer properties: {ex.Message}");
            }
        }

        private void viewLogsButton_Click(object sender, EventArgs e)
        {
            try
            {
                var logsForm = new LogsViewerForm();
                logsForm.ShowDialog(this);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening logs viewer: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

    }
}
