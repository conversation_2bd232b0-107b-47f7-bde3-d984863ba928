using System;
using System.Globalization;
using System.Resources;
using System.Threading;

namespace Mis.Agent.Resources
{
    /// <summary>
    /// Resource manager for handling localized messages in Arabic and English
    /// </summary>
    public static class LocalizedMessages
    {
        private static readonly ResourceManager _resourceManager;
        private static CultureInfo _currentCulture;

        static LocalizedMessages()
        {
            _resourceManager = new ResourceManager("Mis.Agent.Resources.Messages", typeof(LocalizedMessages).Assembly);
            // Default to Arabic culture
            _currentCulture = new CultureInfo("ar-SA");
            Thread.CurrentThread.CurrentUICulture = _currentCulture;
        }

        /// <summary>
        /// Set the current culture for localization
        /// </summary>
        /// <param name="cultureName">Culture name (e.g., "ar-SA" for Arabic, "en-US" for English)</param>
        public static void SetCulture(string cultureName)
        {
            _currentCulture = new CultureInfo(cultureName);
            Thread.CurrentThread.CurrentUICulture = _currentCulture;
        }

        /// <summary>
        /// Get localized message by key
        /// </summary>
        /// <param name="key">Resource key</param>
        /// <param name="args">Format arguments</param>
        /// <returns>Localized message</returns>
        public static string GetMessage(string key, params object[] args)
        {
            try
            {
                string message = _resourceManager.GetString(key, _currentCulture) ?? key;
                return args.Length > 0 ? string.Format(message, args) : message;
            }
            catch
            {
                return key; // Return key if resource not found
            }
        }

        // Common messages
        public static string ApplicationAlreadyRunning => GetMessage("ApplicationAlreadyRunning");
        public static string ApplicationRunningTitle => GetMessage("ApplicationRunningTitle");
        public static string Error => GetMessage("Error");
        public static string Success => GetMessage("Success");
        public static string Warning => GetMessage("Warning");
        public static string SaveSettings => GetMessage("SaveSettings");
        public static string SettingsSaved => GetMessage("SettingsSaved");
        public static string PluginError => GetMessage("PluginError");
        public static string PluginsFolderNotFound => GetMessage("PluginsFolderNotFound");

        // Tab names
        public static string NotificationsTab => GetMessage("NotificationsTab");
        public static string PrintTab => GetMessage("PrintTab");
        public static string ScannerTab => GetMessage("ScannerTab");
        public static string BarcodeTab => GetMessage("BarcodeTab");
        public static string PortTab => GetMessage("PortTab");

        // Methods with parameters
        public static string FormDisplayError(string error) => GetMessage("FormDisplayError", error);
        public static string PluginExecutionError(string pluginName, string error) => GetMessage("PluginExecutionError", pluginName, error);
        public static string PluginLoadError(string pluginName, string error) => GetMessage("PluginLoadError", pluginName, error);
        public static string InvalidTabPage(string pluginName) => GetMessage("InvalidTabPage", pluginName);
        public static string SettingsSaveError(string error) => GetMessage("SettingsSaveError", error);
        public static string PortInUse(int port) => GetMessage("PortInUse", port);
    }
}
