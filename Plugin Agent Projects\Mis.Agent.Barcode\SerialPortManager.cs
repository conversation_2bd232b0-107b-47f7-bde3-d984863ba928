﻿//using Mis.Shared.Interface;
//using Microsoft.AspNetCore.SignalR.Client;
//using System;
//using System.IO.Ports;
//using System.Text;

//namespace Mis.Agent.Barcode
//{
//    public class SerialPortManager
//    {
//        private static readonly SerialPortManager _instance = new SerialPortManager();
//        public SerialPort _serialPort;
//        public bool IsCaptureImageMode { get; set; } // Property to store the capture mode state
//        private HubConnection _hubConnection; // Added HubConnection field
//        private bool? _isCaptureImageMode;
//        public string _lastScannedBase64Data; // Store last scanned Base64 data
//        private SerialPortManager() { }

//        public static SerialPortManager Instance => _instance;
//        public bool IsPortOpen => _serialPort != null && _serialPort.IsOpen;

//        public event SerialDataReceivedEventHandler DataReceived;
//        public void StartListening(string comPort = null)
//        {
//            try
//            {
//                if (IsPortOpen)
//                {
//                    // Already open: consider logging or handling this case
//                    return;
//                }
//                // Check if the specified COM port is available
//                if (!Array.Exists(SerialPort.GetPortNames(), port => port.Equals(comPort, StringComparison.OrdinalIgnoreCase)))
//                {
//                    MessageBox.Show($"COM port {comPort} is not available.");
//                    return;
//                }
//                // Force release the port
//                ForceReleasePort();
//                try
//                {
//                    // Create a new SerialPort instance
//                    _serialPort = new SerialPort(comPort, 9600, Parity.None, 8, StopBits.One);
//                    // Subscribe to the DataReceived event
//                    _serialPort.DataReceived += OnDataReceived;

//                    // Open the serial port
//                    _serialPort.Open();
//                    return; // Success
//                }
//                catch (Exception ex)
//                {
//                    MessageBox.Show($"Error opening COM port: {ex.Message}");
//                    return;
//                }

//            }
//            catch (Exception ex)
//            {
//                MessageBox.Show($"Error: {ex.Message}");
//            }
//        }

//        public void ForceReleasePort()
//        {
//            if (_serialPort != null)
//            {
//                try
//                {
//                    // Unsubscribe from the event
//                    _serialPort.DataReceived -= OnDataReceived;

//                    // Close the port if it's open
//                    if (_serialPort.IsOpen)
//                    {
//                        _serialPort.Close();
//                    }

//                    // Use reflection to access the BaseStream and force it to close
//                    var baseStream = _serialPort.BaseStream;
//                    if (baseStream != null)
//                    {
//                        baseStream.Close();
//                        baseStream.Dispose();
//                    }

//                    // Dispose of the SerialPort
//                    _serialPort.Dispose();
//                    _serialPort = null; // Set to null to avoid reuse
//                }
//                catch (Exception ex)
//                {
//                    Console.WriteLine($"Error releasing COM port: {ex.Message}");
//                }
//            }
//        }




//        public void SetCaptureImageMode(bool mode)
//        {
//            IsCaptureImageMode = mode; // Set the current capturing mode
//        }


//        public void Write(string command)
//        {
//            if (_serialPort != null && _serialPort.IsOpen)
//            {
//                _serialPort.Write(command);
//            }
//            else
//            {
//                throw new InvalidOperationException("Cannot write to the port because it is not open.");
//            }
//        }
//        // Event handler for DataReceived event
//        private void OnDataReceived(object sender, SerialDataReceivedEventArgs e)
//        {
//            // Raise the DataReceived event for subscribers
//            DataReceived?.Invoke(sender, e);
//        }

//        public string[] GetAvailableCOMPorts()
//        {
//            return SerialPort.GetPortNames(); // Return available COM ports
//        }
//    }
//}