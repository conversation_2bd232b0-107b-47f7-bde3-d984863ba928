using Mis.Shared.Interface;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Mis.Agent.Port
{
    public class PortService : IPortAppService
    {
        private PortForm _portForm;
        private readonly IConfiguration _configuration;
        public event Action<bool> NotificationStateChanged;
        private readonly string _filePath;
        bool _notificationsEnabled = true;

        private readonly Dictionary<string, string> _settings;

        public string PluginName => "Port Plugin";
        public string PluginVersion => "1.0.0";

        public async Task Execute()
        {
            // Initialize port service components
            try
            {
                // Validate configuration settings
                var baseUrl = GetBaseUrl();
                if (string.IsNullOrEmpty(baseUrl))
                {
                    Console.WriteLine($"Warning: {PluginName} - No base URL configured");
                }

                // Log plugin startup
                Console.WriteLine($"{PluginName} v{PluginVersion} initialized successfully");
                Console.WriteLine($"Base URL: {baseUrl}");

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing {PluginName}: {ex.Message}");
                throw;
            }
        }

        public PortService()
        {
            _filePath = Path.Combine(AppContext.BaseDirectory, "appsettings.json");

            if (!File.Exists(_filePath))
            {
                MessageBox.Show($"Missing configuration file: {_filePath}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Environment.Exit(1);
            }

            _configuration = new ConfigurationBuilder()
                .SetBasePath(AppContext.BaseDirectory)
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .Build();

            NotificationManager.NotificationEvent += OnNotificationReceived;
            _portForm = new PortForm(this);
            _settings = LoadSettings();
        }


        public async Task Excute()
        {
            await Task.CompletedTask;
        }


        public object GetTabPage()
        {

            return _portForm.PortTap; // Return the TabPage as an object
        }
        private void OnNotificationReceived(object sender, NotificationEventArgs e)
        {
            _notificationsEnabled = e.IsEnabled; // Additional logic based on notification state
        }
        public void ShowNotification(string title, string text)
        {
            if (_notificationsEnabled)
            {
                using (var notifyIcon = new NotifyIcon
                {
                    Icon = SystemIcons.Information,
                    Visible = true,
                    BalloonTipTitle = title,
                    BalloonTipText = text
                })
                {
                    notifyIcon.ShowBalloonTip(1000);
                    Task.Delay(1000).ContinueWith(t => notifyIcon.Dispose());
                }
            }
        }


        private Dictionary<string, string> LoadSettings()
        {
            try
            {
                var settings = new Dictionary<string, string>();

                // Load the Server section for BaseUrl
                var serverSection = _configuration.GetSection("Server");
                if (serverSection != null)
                {
                    settings["BaseUrl"] = serverSection["BaseUrl"];
                }

                return settings;
            }
            catch (Exception ex)
            {
                throw new Exception("Error loading settings", ex);
            }
        }

        public string GetBaseUrl()
        {
            var url = _configuration.GetValue<string>("Server:BaseUrl");

            // Default to HTTPS localhost if no URL is set
            if (string.IsNullOrWhiteSpace(url))
            {
                url = "https://localhost:5001";
            }
            return url;
        }
        public string GetBarcodeBaseUrl()
        {
            var url = _configuration.GetValue<string>("Barcode:BarcodeBaseUrl");

            // Default to HTTPS localhost if no URL is set
            if (string.IsNullOrWhiteSpace(url))
            {
                url = "http://localhost:5002";
            }

            return url;
        }
        public string GetCOMPort()
        {
            var port = _configuration.GetValue<string>("DefaultCOMPort:COMPort");

            return port;
        }
        public void UpdateBaseUrl(string newBaseUrl)
        {
            // Load the appsettings.json file
            var json = File.ReadAllText(_filePath);
            dynamic jsonObj = Newtonsoft.Json.JsonConvert.DeserializeObject(json);

            // Modify the BaseUrl
            jsonObj["Server"]["BaseUrl"] = newBaseUrl;

            // Write the modified configuration back to the file
            string output = Newtonsoft.Json.JsonConvert.SerializeObject(jsonObj, Newtonsoft.Json.Formatting.Indented);
            File.WriteAllText(_filePath, output);

            // Update the in-memory settings
            _settings["BaseUrl"] = newBaseUrl;
        }




        public void UpdateBarcodeBaseUrl(string newBaseUrl)
        {
            try
            {
                if (string.IsNullOrEmpty(newBaseUrl) /*|| !result*/)
                {
                    MessageBox.Show("Please enter a valid URL.", "Invalid URL", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Ensure the URL starts with 'http://' and not 'https://'
                if (newBaseUrl.StartsWith("https://"))
                {
                    newBaseUrl = newBaseUrl.Replace("https://", "http://");
                }
                else if (!newBaseUrl.StartsWith("http://"))
                {
                    // Add 'http://' if it's missing
                    newBaseUrl = "http://" + newBaseUrl;
                }

                // Ensure the URL ends with '/chatHub'
                if (!newBaseUrl.EndsWith("/chatHub"))
                {
                    newBaseUrl = newBaseUrl.TrimEnd('/') + "/chatHub";  // Trim any extra '/' before appending
                }

                // Load the appsettings.json file
                var json = File.ReadAllText(_filePath);
                dynamic jsonObj = Newtonsoft.Json.JsonConvert.DeserializeObject(json);

                // Modify the BarcodeBaseUrl
                jsonObj["Barcode"]["BarcodeBaseUrl"] = newBaseUrl;

                // Write the modified configuration back to the file
                string output = Newtonsoft.Json.JsonConvert.SerializeObject(jsonObj, Newtonsoft.Json.Formatting.Indented);
                File.WriteAllText(_filePath, output);

                // Update the in-memory settings
                _settings["BarcodeBaseUrl"] = newBaseUrl;
            }
            catch (Exception ex)
            {
                // Handle any errors appropriately
                MessageBox.Show($"Error updating the Barcode URL: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                throw;
            }
        }

        public bool IsValidBarcodeUrl(string url)
        {
            // Check if the URL starts with 'http://localhost' and ends with '/chatHub'
            if (Uri.TryCreate(url, UriKind.Absolute, out Uri uriResult) &&
                uriResult.Scheme == Uri.UriSchemeHttp &&
                uriResult.LocalPath == "/chatHub")
            {
                // Check if the port is valid (between 1 and 65535)
                return uriResult.Port > 0 && uriResult.Port <= 65535;
            }

            return false;
        }

        public void UpdateCOMPort(string selectedComPort)
        {
            try
            {
                if (string.IsNullOrEmpty(selectedComPort) /*|| !result*/)
                {
                    MessageBox.Show("Please enter a valid URL.", "Invalid URL", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                // Load the appsettings.json file
                var json = File.ReadAllText(_filePath);
                dynamic jsonObj = Newtonsoft.Json.JsonConvert.DeserializeObject(json);

                // Modify the BarcodeBaseUrl
                jsonObj["DefaultCOMPort"]["COMPort"] = selectedComPort;

                // Write the modified configuration back to the file
                string output = Newtonsoft.Json.JsonConvert.SerializeObject(jsonObj, Newtonsoft.Json.Formatting.Indented);
                File.WriteAllText(_filePath, output);

                // Update the in-memory settings
                _settings["COMPort"] = selectedComPort;
            }
            catch (Exception ex)
            {
                // Handle any errors appropriately
                MessageBox.Show($"Error updating the COM Port: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                throw;
            }
        }

    }
}
