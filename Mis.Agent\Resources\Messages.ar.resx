<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- Arabic Messages -->
  <data name="ApplicationAlreadyRunning" xml:space="preserve">
    <value>تطبيق MIS Agent يعمل بالفعل. لا يمكن تشغيل أكثر من نسخة واحدة.</value>
  </data>
  <data name="ApplicationRunningTitle" xml:space="preserve">
    <value>تطبيق قيد التشغيل</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>خطأ</value>
  </data>
  <data name="FormDisplayError" xml:space="preserve">
    <value>حدث خطأ في عرض النموذج: {0}</value>
  </data>
  <data name="PluginExecutionError" xml:space="preserve">
    <value>خطأ في تنفيذ الإضافة {0}: {1}</value>
  </data>
  <data name="PluginLoadError" xml:space="preserve">
    <value>خطأ في تحميل الإضافة {0}: {1}</value>
  </data>
  <data name="PluginsFolderNotFound" xml:space="preserve">
    <value>مجلد الإضافات غير موجود.</value>
  </data>
  <data name="InvalidTabPage" xml:space="preserve">
    <value>الإضافة {0} لم ترجع صفحة تبويب صالحة.</value>
  </data>
  <data name="PluginError" xml:space="preserve">
    <value>خطأ في الإضافة</value>
  </data>
  <data name="SaveSettings" xml:space="preserve">
    <value>حفظ جميع الإعدادات</value>
  </data>
  <data name="SettingsSaved" xml:space="preserve">
    <value>تم حفظ جميع الإعدادات بنجاح.</value>
  </data>
  <data name="SettingsSaveError" xml:space="preserve">
    <value>خطأ في حفظ الإعدادات: {0}</value>
  </data>
  <data name="Success" xml:space="preserve">
    <value>نجح</value>
  </data>
  <data name="Warning" xml:space="preserve">
    <value>تحذير</value>
  </data>
  <data name="PortInUse" xml:space="preserve">
    <value>المنفذ {0} مستخدم بالفعل من قبل تطبيق آخر.</value>
  </data>
  <data name="RestartRequired" xml:space="preserve">
    <value>يتطلب إعادة تشغيل التطبيق لتطبيق التغييرات.</value>
  </data>
  <data name="RestartNow" xml:space="preserve">
    <value>هل تريد إعادة تشغيل التطبيق الآن؟</value>
  </data>
  
  <!-- Tab Names in Arabic -->
  <data name="NotificationsTab" xml:space="preserve">
    <value>الإشعارات</value>
  </data>
  <data name="PrintTab" xml:space="preserve">
    <value>الطباعة</value>
  </data>
  <data name="ScannerTab" xml:space="preserve">
    <value>الماسح الضوئي</value>
  </data>
  <data name="BarcodeTab" xml:space="preserve">
    <value>الباركود</value>
  </data>
  <data name="PortTab" xml:space="preserve">
    <value>المنافذ</value>
  </data>
</root>
