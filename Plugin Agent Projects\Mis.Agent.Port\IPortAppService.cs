using Mis.Shared.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Mis.Agent.Port
{
    public interface IPortAppService : IPlugin
    {
        void UpdateBaseUrl(string newBaseUrl);
        void UpdateBarcodeBaseUrl(string newBaseUrl);
        void UpdateCOMPort(string selectedComPort);
        bool IsValidBarcodeUrl(string url);
    }
}
