using Mis.Shared.Interface;
using Microsoft.Extensions.DependencyInjection;

namespace Mis.Agent.Print
{
    internal static class Program
    {
        /// <summary>
        ///  The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            // Initialize the application configuration
            ApplicationConfiguration.Initialize();

            // Build the service provider
            var serviceProvider = ConfigureServices();

            // Resolve the main form
            var mainForm = serviceProvider.GetRequiredService<NotificationsForm>();

            // Run the application
            Application.Run(mainForm);
        }

        private static IServiceProvider ConfigureServices()
        {
            var services = new ServiceCollection();

            // Register services here
            services.AddTransient<INotificationAppService, NotificationsService>();
            services.AddSingleton<NotificationsForm>(); // Register your main form
            return services.BuildServiceProvider();
        }
    }
}