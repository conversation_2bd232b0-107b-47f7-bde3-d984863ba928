<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- English Messages -->
  <data name="ApplicationAlreadyRunning" xml:space="preserve">
    <value>MIS Agent is already running. Only one instance can run at a time.</value>
  </data>
  <data name="ApplicationRunningTitle" xml:space="preserve">
    <value>Application Running</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="FormDisplayError" xml:space="preserve">
    <value>An error occurred while displaying the form: {0}</value>
  </data>
  <data name="PluginExecutionError" xml:space="preserve">
    <value>Error executing plugin {0}: {1}</value>
  </data>
  <data name="PluginLoadError" xml:space="preserve">
    <value>Error loading plugin {0}: {1}</value>
  </data>
  <data name="PluginsFolderNotFound" xml:space="preserve">
    <value>Plugins folder not found.</value>
  </data>
  <data name="InvalidTabPage" xml:space="preserve">
    <value>Plugin {0} did not return a valid TabPage.</value>
  </data>
  <data name="PluginError" xml:space="preserve">
    <value>Plugin Error</value>
  </data>
  <data name="SaveSettings" xml:space="preserve">
    <value>Save All Settings</value>
  </data>
  <data name="SettingsSaved" xml:space="preserve">
    <value>All settings have been saved successfully.</value>
  </data>
  <data name="SettingsSaveError" xml:space="preserve">
    <value>Error saving settings: {0}</value>
  </data>
  <data name="Success" xml:space="preserve">
    <value>Success</value>
  </data>
  <data name="Warning" xml:space="preserve">
    <value>Warning</value>
  </data>
  <data name="PortInUse" xml:space="preserve">
    <value>Port {0} is already in use by another application.</value>
  </data>
  <data name="RestartRequired" xml:space="preserve">
    <value>Application restart is required for changes to take effect.</value>
  </data>
  <data name="RestartNow" xml:space="preserve">
    <value>Do you want to restart the application now?</value>
  </data>
</root>
