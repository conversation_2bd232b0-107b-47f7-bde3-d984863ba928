﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <EnableWindowsTargeting>true</EnableWindowsTargeting>
    <AssemblyName>MIS.Agent.Print</AssemblyName>
    <RootNamespace>MIS.Agent.Print</RootNamespace>
    <Version>1.0.3</Version>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Product>MIS Agent System</Product>
    <Company>MIS Company</Company>
    <Copyright>Copyright © MIS Company 2024</Copyright>
    <Description>MIS Agent Print Plugin - Print and Notification Services</Description>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Cors" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Core" Version="2.2.5" />
    <PackageReference Include="PdfiumViewer.Core" Version="1.0.4" />
    <PackageReference Include="System.Data.SQLite" Version="1.0.119" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.6" />


  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Mis.Shared.Interface\Mis.Shared.Interface.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="IronPdf.Core">
      <HintPath>\IronPdf.Core\IronPdf.Core.dll</HintPath>
    </Reference>
  </ItemGroup>

  <!-- Digital Signing Target -->
  <Target Name="PostBuildSigning" AfterTargets="Build" Condition="'$(Configuration)' == 'Release'">
    <PropertyGroup>
      <SignToolPath>C:\Program Files (x86)\Windows Kits\10\bin\10.0.26100.0\x64\signtool.exe</SignToolPath>
      <CertificatePath>$(ProjectDir)..\..\Mis.Agent\MisCodeSign.pfx</CertificatePath>
      <CertificatePassword>MisCompanyPassword123!</CertificatePassword>
    </PropertyGroup>

    <Exec
      Command='"$(SignToolPath)" sign /f "$(CertificatePath)" /p "$(CertificatePassword)" /tr http://timestamp.digicert.com /td sha256 /fd sha256 "$(TargetPath)"'
      Condition="Exists('$(CertificatePath)')"
      ContinueOnError="true" />
  </Target>
</Project>