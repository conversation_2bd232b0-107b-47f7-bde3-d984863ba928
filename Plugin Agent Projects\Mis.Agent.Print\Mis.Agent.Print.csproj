﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <EnableWindowsTargeting>true</EnableWindowsTargeting>
    <AssemblyName>MIS.Agent.Print</AssemblyName>
    <RootNamespace>MIS.Agent.Print</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Cors" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Core" Version="2.2.5" />
    <PackageReference Include="PdfiumViewer.Core" Version="1.0.4" />
    <PackageReference Include="System.Data.SQLite" Version="1.0.119" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.6" />


  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Mis.Shared.Interface\Mis.Shared.Interface.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="IronPdf.Core">
      <HintPath>\IronPdf.Core\IronPdf.Core.dll</HintPath>
    </Reference>
  </ItemGroup>
</Project>