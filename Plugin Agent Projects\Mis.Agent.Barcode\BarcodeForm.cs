using Mis.Shared.Interface;
using System.Diagnostics;

namespace Mis.Agent.Barcode
{
    public partial class BarcodeForm : Form
    {
        private readonly IBarcodeAppService _barcodeAppService;
        public BarcodeForm(IBarcodeAppService barcodeAppService)
        {
            _barcodeAppService = barcodeAppService;
            InitializeComponent();
        }
        private async void SaveBarcodeConfiguration_Click(object sender, EventArgs e)
        {
            try
            {
                // Validate and update the URL
                string newBaseUrl = barcodeUrlTextBox.Text;
                bool result = _barcodeAppService.IsValidBarcodeUrl(newBaseUrl);

                if (string.IsNullOrEmpty(newBaseUrl) /*|| !result*/)
                {
                    MessageBox.Show("Please enter a valid URL.", "Invalid URL", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Update the BaseUrl in appsettings.json if it's different
                var currentUrl = _barcodeAppService.GetBarcodeBaseUrl();
                if (currentUrl != newBaseUrl)
                {
                    _barcodeAppService.UpdateBarcodeBaseUrl(newBaseUrl);
                    MessageBox.Show("URL updated successfully. Restarting the application...", "Update Successful", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                // Get the selected COM port from the ComboBox
                string selectedComPort = comboBoxCOMPorts.SelectedItem as string;
                if (string.IsNullOrEmpty(selectedComPort))
                {
                    MessageBox.Show("الرجاء اختيار منفذ COM.", "لم يتم اختيار منفذ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (!ConfirmBarcodePortAvailability(selectedComPort))
                    return;

                _barcodeAppService.UpdateCOMPort(selectedComPort);

                // Display the selected COM port in textBox
                comPortTextBox.Text = selectedComPort;

                // Update port status
                UpdatePortStatus(selectedComPort);

                MessageBox.Show("تم حفظ إعدادات الباركود بنجاح. سيتم تطبيق التغييرات عند إعادة تشغيل التطبيق.",
                              "تم الحفظ", MessageBoxButtons.OK, MessageBoxIcon.Information);

                // Instead of restarting immediately, just save the settings
                // The application will use the new settings on next startup

            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        private bool ConfirmBarcodePortAvailability(string selectedComPort)
        {
            if (!_barcodeAppService.IsBarcodeScannerConnected(selectedComPort))
            {
                var result = MessageBox.Show(
                    $"المنفذ {selectedComPort} غير متاح أو لا يحتوي على جهاز باركود. هل تريد المتابعة؟",
                    "تحذير المنفذ",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Warning);

                return result == DialogResult.Yes;
            }

            return true;
        }
        private void ButtonTestPort_Click(object sender, EventArgs e)
        {
            try
            {
                string selectedComPort = comboBoxCOMPorts.SelectedItem as string;
                if (string.IsNullOrEmpty(selectedComPort))
                {
                    MessageBox.Show("الرجاء اختيار منفذ COM أولاً.", "لم يتم اختيار منفذ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                bool isConnected = _barcodeAppService.IsBarcodeScannerConnected(selectedComPort);
                UpdatePortStatus(selectedComPort, isConnected);

                if (isConnected)
                {
                    MessageBox.Show($"المنفذ {selectedComPort} متاح ويعمل بشكل صحيح.", "اختبار المنفذ", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show($"المنفذ {selectedComPort} غير متاح أو لا يحتوي على جهاز باركود.", "اختبار المنفذ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختبار المنفذ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdatePortStatus(string portName, bool? isConnected = null)
        {
            if (isConnected == null)
            {
                isConnected = _barcodeAppService.IsBarcodeScannerConnected(portName);
            }

            if (isConnected.Value)
            {
                labelPortStatus.Text = $"حالة المنفذ: {portName} - متصل ✓";
                labelPortStatus.ForeColor = Color.Green;
            }
            else
            {
                labelPortStatus.Text = $"حالة المنفذ: {portName} - غير متصل ✗";
                labelPortStatus.ForeColor = Color.Red;
            }
        }

        private void RestartApplication()
        {
            try
            {
                // Close the current application
                Application.Exit();

                // Start a new instance of the application
                Process.Start(Application.ExecutablePath);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to restart the application: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                // Optionally log the exception
            }
        }

    }
}
