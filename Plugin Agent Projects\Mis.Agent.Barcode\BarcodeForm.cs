using Mis.Shared.Interface;
using System.Diagnostics;

namespace Mis.Agent.Barcode
{
    public partial class BarcodeForm : Form
    {
        private readonly IBarcodeAppService _barcodeAppService;
        public BarcodeForm(IBarcodeAppService barcodeAppService)
        {
            _barcodeAppService = barcodeAppService;
            InitializeComponent();
        }
        private async void SaveBarcodeConfiguration_Click(object sender, EventArgs e)
        {
            try
            {
                // Validate and update the URL
                string newBaseUrl = barcodeUrlTextBox.Text;
                bool result = _barcodeAppService.IsValidBarcodeUrl(newBaseUrl);

                if (string.IsNullOrEmpty(newBaseUrl) /*|| !result*/)
                {
                    MessageBox.Show("Please enter a valid URL.", "Invalid URL", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Update the BaseUrl in appsettings.json if it's different
                var currentUrl = _barcodeAppService.GetBarcodeBaseUrl();
                if (currentUrl != newBaseUrl)
                {
                    _barcodeAppService.UpdateBarcodeBaseUrl(newBaseUrl);
                    MessageBox.Show("URL updated successfully. Restarting the application...", "Update Successful", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                // Get the selected COM port from the ComboBox
                string selectedComPort = comboBoxCOMPorts.SelectedItem as string;
                if (string.IsNullOrEmpty(selectedComPort))
                {
                    MessageBox.Show("Please select a COM port.", "No COM Port Selected", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                _barcodeAppService.UpdateCOMPort(selectedComPort);


                // Display the selected COM port in textBox5
                comPortTextBox.Text = selectedComPort;
                //// Start listening for data on the COM port
                //_barcodeAppService.PublicInitialize(newBaseUrl, selectedComPort,false);
                //await connection.InvokeAsync("SendMessage", message);
                RestartApplication();

            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to update configuration and send message: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        private void RestartApplication()
        {
            try
            {

                // Close the current application
                Application.Exit();

                // Start a new instance of the application
                Process.Start(Application.ExecutablePath);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to restart the application: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                // Optionally log the exception
            }
        }

    }
}
